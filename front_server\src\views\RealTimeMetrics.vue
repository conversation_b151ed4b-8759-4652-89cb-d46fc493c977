<template>
  <div class="real-time-metrics-page">
    <!-- 面包屑导航 -->
    <div class="breadcrumb">
      <a @click="goBack()">{{ breadcrumbText }}</a>
      <span>></span>
      <span>实时指标</span>
    </div>
    
    <div class="card">
      <div class="card-header">
        <h3>{{ isHistoryMode ? '历史指标详情' : '终端实时指标' }} - <span id="currentDeviceId">{{ identityMac }}</span></h3>
        <div class="header-actions">
<!--          <button -->
<!--            v-if="!isHistoryMode" -->
<!--            class="btn btn-primary" -->
<!--            @click="refreshMetrics" -->
<!--            :disabled="loading"-->
<!--          >-->
<!--            <i>🔄</i> 刷新数据-->
<!--          </button>-->
          <button 
            v-if="isHistoryMode" 
            class="btn btn-secondary" 
            @click="goToRealTime"
          >
            <i>📊</i> 查看实时指标
          </button>
          <button
            class="btn btn-info"
            @click="goToHistoryList"
          >
            <i>📋</i> 历史指标列表
          </button>
        </div>
      </div>
      <div class="card-body">
        <div class="refresh-info" v-if="!isHistoryMode">
          <i>ℹ️</i>
          <span>点击刷新按钮获取最新的终端指标数据，数据更新时间：<span id="lastUpdateTime">{{ lastUpdateTime }}</span></span>
        </div>
        
        <div class="refresh-info" v-else>
          <i>📅</i>
          <span>显示历史时间点的指标数据：{{ historyTime }}</span>
        </div>

        <!-- 时间信息 -->
        <div class="time-info">
          <div>
            <span class="time-label">信息采集时间：</span>
            <span class="time-value" id="metricTime">{{ metricTime }}</span>
          </div>
          <div>
            <span class="time-label">数据接收时间：</span>
            <span class="time-value" id="receiveTime">{{ receiveTime }}</span>
          </div>
          <div v-if="isHistoryMode">
            <span class="time-label">数据类型：</span>
            <span class="time-value history-badge">历史数据</span>
          </div>
        </div>

<!--        &lt;!&ndash; 网络流量信息 &ndash;&gt;-->
<!--        <div v-if="metrics" class="network-info">-->
<!--          <div class="network-card">-->
<!--            <div class="network-title">上行实时速率</div>-->
<!--            <div class="network-value">{{ formatNetworkSpeed(groupBpsValue) }}</div>-->
<!--&lt;!&ndash;            <div class="network-subtitle">总流量速率 ({{ groupBpsValue }} bps)</div>&ndash;&gt;-->
<!--          </div>-->
<!--        </div>-->

<!--        <div v-if="metrics" class="stats-grid">-->
<!--          <div class="stat-card">-->
<!--            <div class="stat-number" :style="{ color: getCpuColor(metrics.cpu.usage) }">{{ formatTemperature(metrics.cpu.temperature) }}</div>-->
<!--            <div class="stat-label">CPU温度</div>-->
<!--          </div>-->
<!--          <div class="stat-card">-->
<!--            <div class="stat-number" :style="{ color: getCpuColor(metrics.cpu.usage) }">{{ formatPercentage(metrics.cpu.usage) }}</div>-->
<!--            <div class="stat-label">CPU使用率</div>-->
<!--          </div>-->
<!--          <div class="stat-card">-->
<!--            <div class="stat-number" :style="{ color: getMemoryColor(memoryPercent) }">{{ formatPercentage(memoryPercent) }}</div>-->
<!--            <div class="stat-label">内存使用率</div>-->
<!--          </div>-->
<!--          <div class="stat-card">-->
<!--            <div class="stat-number" :style="{ color: getDiskColor(metrics.disk.usage) }">{{ formatPercentage(metrics.disk.usage) }}</div>-->
<!--            <div class="stat-label">磁盘使用率</div>-->
<!--          </div>-->
<!--        </div>-->

        <!-- 实时指标趋势图 -->
        <div v-if="metrics" class="trends-container">
          <div class="trends-header">
            <div class="trends-title">
              <span>📈</span>
              <span>实时指标监控</span>
            </div>
            <div class="trends-controls">
              <!-- 时间范围选择 -->
              <div class="time-range-controls">
                <button
                  v-for="range in timeRanges"
                  :key="range.value"
                  class="time-range-selector"
                  :class="{ active: currentTimeRange === range.value }"
                  @click="switchTimeRange(range.value)"
                >
                  {{ range.label }}
                </button>
              </div>

              <!-- 其他控制按钮 -->
              <div class="other-controls">
                <button class="metrics-config-btn" @click="showMetricsConfig">
                  <span>⚙️</span>
                  <span>指标配置</span>
                </button>
              </div>
            </div>
          </div>

          <div class="trends-body">
            <div class="chart-header">
              <div class="chart-title">
                <span>综合指标趋势</span>
                <span class="data-points-info">({{ displayedHistory.length }}个数据点)</span>
              </div>
              <div class="live-indicator" v-if="!isHistoryMode">
                <div class="live-dot"></div>
                <span>实时监控</span>
              </div>
              <div class="chart-legend">
                <div
                  v-for="metric in visibleMetrics"
                  :key="metric.key"
                  class="legend-item clickable"
                  :class="{ 'disabled': !metric.visible }"
                  @click="toggleMetricVisibility(metric.key)"
                >
                  <div class="legend-color" :class="metric.colorClass"></div>
                  <span>{{ metric.label }}</span>
                  <i class="visibility-icon" :class="metric.visible ? 'visible' : 'hidden'">
                    {{ metric.visible ? '👁️' : '👁️‍🗨️' }}
                  </i>
                </div>
              </div>
            </div>

            <div class="chart-wrapper">
              <div ref="chartContainer" class="chart-canvas"></div>

              <!-- 自定义悬浮弹框 -->
              <div
                v-if="customTooltip.visible"
                class="custom-tooltip"
                :style="{
                  left: customTooltip.x + 'px',
                  top: customTooltip.y + 'px'
                }"
              >
                <div class="tooltip-header">
                  <div class="tooltip-time">{{ customTooltip.time }}</div>
                  <div class="tooltip-date">{{ customTooltip.date }}</div>
                </div>
                <div class="tooltip-content">
                  <div
                    v-for="metric in customTooltip.metrics"
                    :key="metric.key"
                    class="tooltip-metric"
                  >
                    <div class="metric-indicator" :style="{ backgroundColor: metric.color }"></div>
                    <div class="metric-info">
                      <div class="metric-name">{{ metric.name }}</div>
                      <div class="metric-value" :style="{ color: metric.color }">{{ metric.value }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 当前值显示 -->
            <div class="current-values">
              <div
                v-for="metric in visibleMetrics"
                :key="metric.key"
                v-show="metric.visible && metric.enabled"
                class="value-card"
              >
                <div class="value-info">
                  <div class="value-title">{{ metric.label.replace(/\s*\([^)]*\)/, '') }}</div>
                  <div class="value-number" :class="metric.numberClass">{{ getMetricCurrentValue(metric.key) }}</div>
                </div>
                <div class="value-change" :class="getChangeClass(getMetricChange(metric.key))">
                  {{ formatChange(getMetricChange(metric.key)) }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 指标配置弹框 -->
        <div v-if="metricsConfigVisible" class="metrics-config-modal" @click="hideMetricsConfig">
          <div class="metrics-config-content" @click.stop>
            <div class="config-header">
              <h3>自定义指标监控</h3>
              <button class="close-btn" @click="hideMetricsConfig">&times;</button>
            </div>
            <div class="config-body">
              <div class="config-section">
                <div class="section-header">
                  <h4>选择要监控的指标</h4>
                  <div class="metrics-limit-info" :class="{ 'limit-warning': enabledMetricsCount >= 4 }">
                    已选择 {{ enabledMetricsCount }}/4 个指标
                  </div>
                </div>
                <div class="metrics-categories">
                  <div
                    v-for="category in metricCategories"
                    :key="category"
                    class="category-section"
                  >
                    <div class="category-header">
                      <h5>{{ category }}</h5>
                      <span class="category-count">
                        ({{ getCategoryEnabledCount(category) }}/{{ getCategoryTotalCount(category) }})
                      </span>
                    </div>
                    <div class="metrics-list">
                      <div
                        v-for="metric in getMetricsByCategory(category)"
                        :key="metric.key"
                        class="metric-item"
                      >
                        <label class="metric-checkbox">
                          <input
                            type="checkbox"
                            v-model="metric.enabled"
                            @change="(event) => updateMetricConfig(event, metric.key)"
                          >
                          <span class="checkmark"></span>
                          <div class="metric-info">
                            <div class="metric-name">{{ metric.label }}</div>
                            <div class="metric-desc">{{ metric.description }}</div>
                          </div>
                        </label>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="config-section">
                <h4>已选择的指标 ({{ enabledMetricsCount }}/{{ allMetrics.length }})</h4>
                <div class="selected-metrics">
                  <div
                    v-for="metric in enabledMetrics"
                    :key="metric.key"
                    class="selected-metric-item"
                  >
                    <div class="legend-color" :class="metric.colorClass"></div>
                    <span class="metric-name">{{ metric.label }}</span>
                    <button
                      class="visibility-toggle"
                      :class="{ 'visible': metric.visible }"
                      @click="toggleMetricVisibility(metric.key)"
                    >
                      {{ metric.visible ? '显示' : '隐藏' }}
                    </button>
                  </div>
                </div>
              </div>
            </div>
            <div class="config-footer">
              <button class="btn-secondary" @click="resetMetricsConfig">重置默认</button>
              <button class="btn-primary" @click="applyMetricsConfig">应用配置</button>
            </div>
          </div>
        </div>

        <div v-if="metrics" class="card">
          <div class="card-header">
            <h4>详细指标信息</h4>
          </div>
          <div class="card-body">
            <div class="metrics-grid">
              <div class="metric-section system-section">
                <h5><i class="icon-system"></i>系统信息</h5>
                <div class="metric-row">
                  <div class="metric-label">系统运行时长</div>
                  <div class="metric-value" id="uptime">{{ uptime }}</div>
                </div>
                <div class="metric-row">
                  <div class="metric-label">设备温度</div>
                  <div class="metric-value">
                    SOC温度: {{ formatTemperature(socTemperature) }}<br>
                    GPU温度: {{ formatTemperature(gpuTemperature) }}
                  </div>
                </div>
              </div>
              
              <div class="metric-section cpu-section">
                <h5><i class="icon-cpu"></i>CPU信息</h5>
                <div class="metric-row">
                  <div class="metric-label">CPU核心</div>
                  <div class="metric-value">{{ metrics.cpu.cores }}核{{ metrics.cpu.cores }}线程</div>
                </div>
                <div class="metric-row">
                  <div class="metric-label">CPU使用率</div>
                  <div class="metric-value">
                    用户态: {{ formatPercentage(cpuUserUsage) }} | 系统态: {{ formatPercentage(cpuSystemUsage) }} | 空闲: {{ formatPercentage(cpuIdleUsage) }}
                    <div class="progress-bar">
                      <div class="progress-fill" :style="{ width: metrics.cpu.usage + '%', backgroundColor: getCpuColor(metrics.cpu.usage) }"></div>
                    </div>
                  </div>
                </div>
              </div>
              
              <div class="metric-section memory-section">
                <h5><i class="icon-memory"></i>内存信息</h5>

                <!-- 内存总览 -->
                <div class="memory-overview">
                  <div class="memory-summary">
                    <div class="memory-item">
                      <span class="memory-label">总内存</span>
                      <span class="memory-value">{{ formatBytes(memoryTotal) }}</span>
                    </div>
                    <div class="memory-item">
                      <span class="memory-label">已用内存</span>
                      <span class="memory-value">{{ formatBytes(memoryUsed) }}</span>
                    </div>
                    <div class="memory-item">
                      <span class="memory-label">可用内存</span>
                      <span class="memory-value">{{ formatBytes(memoryAvailable) }}</span>
                    </div>
                    <div class="memory-item">
                      <span class="memory-label">使用率</span>
                      <span class="memory-value">{{ formatPercentage(memoryPercent) }}</span>
                    </div>
                  </div>
                  <div class="progress-bar">
                    <div class="progress-fill" :style="{ width: memoryPercent + '%', backgroundColor: getMemoryColor(memoryPercent) }"></div>
                  </div>
                </div>

                <!-- 内存详细分布 -->
<!--                <div class="memory-details">-->
<!--                  <div class="memory-detail-title">内存分布详情</div>-->
<!--                  <div class="memory-detail-grid">-->
<!--                    <div class="memory-detail-item">-->
<!--                      <span class="detail-label">空闲内存</span>-->
<!--                      <span class="detail-value">{{ formatBytes(memoryFree) }}</span>-->
<!--                    </div>-->
<!--                    <div class="memory-detail-item">-->
<!--                      <span class="detail-label">活跃内存</span>-->
<!--                      <span class="detail-value">{{ formatBytes(memoryActive) }}</span>-->
<!--                    </div>-->
<!--                    <div class="memory-detail-item">-->
<!--                      <span class="detail-label">非活跃内存</span>-->
<!--                      <span class="detail-value">{{ formatBytes(memoryInactive) }}</span>-->
<!--                    </div>-->
<!--                    <div class="memory-detail-item">-->
<!--                      <span class="detail-label">缓冲区</span>-->
<!--                      <span class="detail-value">{{ formatBytes(memoryBuffers) }}</span>-->
<!--                    </div>-->
<!--                    <div class="memory-detail-item">-->
<!--                      <span class="detail-label">文件缓存</span>-->
<!--                      <span class="detail-value">{{ formatBytes(memoryCached) }}</span>-->
<!--                    </div>-->
<!--                    <div class="memory-detail-item">-->
<!--                      <span class="detail-label">共享内存</span>-->
<!--                      <span class="detail-value">{{ formatBytes(memoryShared) }}</span>-->
<!--                    </div>-->
<!--                    <div class="memory-detail-item">-->
<!--                      <span class="detail-label">内核缓存</span>-->
<!--                      <span class="detail-value">{{ formatBytes(memorySlab) }}</span>-->
<!--                    </div>-->
<!--                  </div>-->
<!--                </div>-->
              </div>
              
              <div class="metric-section storage-section">
                <div class="storage-section-header">
                  <h5><i class="icon-storage"></i>存储信息</h5>
                  <button class="storage-toggle-btn" @click="toggleStorageExpanded">
                    <span>{{ storageExpanded ? '收起' : '展开详情' }}</span>
                    <i class="toggle-icon" :class="{ 'expanded': storageExpanded }">▼</i>
                  </button>
                </div>

                <!-- 外置存储（默认显示） -->
                <div v-if="hasExternalStorage" class="storage-device primary-storage">
                  <div class="storage-header">
                    <span class="storage-title">外置存储 ({{ externalStorage.device }})</span>
                    <span class="storage-badge primary">主要</span>
                  </div>
                  <div class="metric-row">
                    <div class="metric-label">存储使用</div>
                    <div class="metric-value">
                      总空间: {{ formatBytes(externalStorage.total) }} | 已用: {{ formatBytes(externalStorage.used) }} | 可用: {{ formatBytes(externalStorage.free) }}
                      <div class="progress-bar">
                        <div class="progress-fill" :class="getDiskClass(externalStorage.percent)" :style="{ width: externalStorage.percent + '%' }"></div>
                      </div>
                    </div>
                  </div>
                  <div class="metric-row">
                    <div class="metric-label">IO性能</div>
                    <div class="metric-value">
                      读取: {{ formatDiskIOSpeed(externalStorage.io_rate?.['MBr/s']) }} | 写入: {{ formatDiskIOSpeed(externalStorage.io_rate?.['MBw/s']) }}<br>
                      读取频率: {{ externalStorage.io_rate?.['read/s'] || 0 }}/s | 写入频率: {{ externalStorage.io_rate?.['write/s'] || 0 }}/s
                    </div>
                  </div>

                  <!-- 硬盘健康情况 -->
                  <div v-if="externalStorageSmartmon" class="metric-row">
                    <div class="metric-label">硬盘健康</div>
                    <div class="metric-value">
                      <div class="disk-health-grid">
                        <div class="health-item">
                          <span class="health-label">硬盘温度</span>
                          <span class="health-value">{{ formatDiskTemperature(externalStorageSmartmon.temperature) }}</span>
                        </div>
                        <div class="health-item">
                          <span class="health-label">已使用寿命</span>
                          <span class="health-value" :style="{ color: getDiskHealthColor(externalStorageSmartmon.percentage_used) }">
                            {{ formatDiskLifeUsed(externalStorageSmartmon.percentage_used) }}
                          </span>
                        </div>
                        <div class="health-item">
                          <span class="health-label">总写入量(TBW)</span>
                          <span class="health-value">{{ formatTotalBytesWritten(externalStorageSmartmon.data_units_written) }}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 展开时显示的详细信息 -->
                <div v-if="storageExpanded" class="storage-details">
                  <!-- 内置存储 -->
                  <div class="storage-device secondary-storage">
                    <div class="storage-header">
                      <span class="storage-title">内置存储 ({{ internalStorage.device }})</span>
                      <span class="storage-badge secondary">系统</span>
                    </div>
                    <div class="metric-row">
                      <div class="metric-label">存储使用</div>
                      <div class="metric-value">
                        总空间: {{ formatBytes(internalStorage.total) }} | 已用: {{ formatBytes(internalStorage.used) }} | 可用: {{ formatBytes(internalStorage.free) }}
                        <div class="progress-bar">
                          <div class="progress-fill" :class="getDiskClass(internalStorage.percent)" :style="{ width: internalStorage.percent + '%' }"></div>
                        </div>
                      </div>
                    </div>
                    <div class="metric-row">
                      <div class="metric-label">IO性能</div>
                      <div class="metric-value">
                        读取: {{ formatDiskIOSpeed(internalStorage.io_rate?.['MBr/s']) }} | 写入: {{ formatDiskIOSpeed(internalStorage.io_rate?.['MBw/s']) }}<br>
                        读取频率: {{ internalStorage.io_rate?.['read/s'] || 0 }}/s | 写入频率: {{ internalStorage.io_rate?.['write/s'] || 0 }}/s
                      </div>
                    </div>

                    <!-- 硬盘健康情况 -->
                    <div v-if="internalStorageSmartmon" class="metric-row">
                      <div class="metric-label">硬盘健康</div>
                      <div class="metric-value">
                        <div class="disk-health-grid">
                          <div class="health-item">
                            <span class="health-label">硬盘温度</span>
                            <span class="health-value">{{ formatDiskTemperature(internalStorageSmartmon.temperature) }}</span>
                          </div>
                          <div class="health-item">
                            <span class="health-label">已使用寿命</span>
                            <span class="health-value" :style="{ color: getDiskHealthColor(internalStorageSmartmon.percentage_used) }">
                              {{ formatDiskLifeUsed(internalStorageSmartmon.percentage_used) }}
                            </span>
                          </div>
                          <div class="health-item">
                            <span class="health-label">总写入量(TBW)</span>
                            <span class="health-value">{{ formatTotalBytesWritten(internalStorageSmartmon.data_units_written) }}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 如果没有外置存储，在展开时显示外置存储为空的提示 -->
                  <div v-if="!hasExternalStorage" class="storage-device empty-storage">
                    <div class="storage-header">
                      <span class="storage-title">外置存储</span>
                      <span class="storage-badge empty">未检测到</span>
                    </div>
                    <div class="metric-row">
                      <div class="metric-label">状态</div>
                      <div class="metric-value">未检测到外置存储设备</div>
                    </div>
                  </div>
                </div>

                <!-- 如果没有外置存储且未展开，显示内置存储作为主要存储 -->
                <div v-if="!hasExternalStorage && !storageExpanded" class="storage-device primary-storage">
                  <div class="storage-header">
                    <span class="storage-title">内置存储 ({{ internalStorage.device }})</span>
                    <span class="storage-badge primary">主要</span>
                  </div>
                  <div class="metric-row">
                    <div class="metric-label">存储使用</div>
                    <div class="metric-value">
                      总空间: {{ formatBytes(internalStorage.total) }} | 已用: {{ formatBytes(internalStorage.used) }} | 可用: {{ formatBytes(internalStorage.free) }}
                      <div class="progress-bar">
                        <div class="progress-fill" :class="getDiskClass(internalStorage.percent)" :style="{ width: internalStorage.percent + '%' }"></div>
                      </div>
                    </div>
                  </div>
                  <div class="metric-row">
                    <div class="metric-label">IO性能</div>
                    <div class="metric-value">
                      读取: {{ formatDiskIOSpeed(internalStorage.io_rate?.['MBr/s']) }} | 写入: {{ formatDiskIOSpeed(internalStorage.io_rate?.['MBw/s']) }}<br>
                      读取频率: {{ internalStorage.io_rate?.['read/s'] || 0 }}/s | 写入频率: {{ internalStorage.io_rate?.['write/s'] || 0 }}/s
                    </div>
                  </div>

                  <!-- 硬盘健康情况 -->
                  <div v-if="internalStorageSmartmon" class="metric-row">
                    <div class="metric-label">硬盘健康</div>
                    <div class="metric-value">
                      <div class="disk-health-grid">
                        <div class="health-item">
                          <span class="health-label">硬盘温度</span>
                          <span class="health-value">{{ formatDiskTemperature(internalStorageSmartmon.temperature) }}</span>
                        </div>
                        <div class="health-item">
                          <span class="health-label">已使用寿命</span>
                          <span class="health-value" :style="{ color: getDiskHealthColor(internalStorageSmartmon.percentage_used) }">
                            {{ formatDiskLifeUsed(internalStorageSmartmon.percentage_used) }}
                          </span>
                        </div>
                        <div class="health-item">
                          <span class="health-label">总写入量(TBW)</span>
                          <span class="health-value">{{ formatTotalBytesWritten(internalStorageSmartmon.data_units_written) }}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div class="metric-section data-section">
                <h5><i class="icon-file"></i>数据文件</h5>
                <div class="metric-row">
                  <div class="metric-label">原始文件(原始数据待压缩文件)</div>
                  <div class="metric-value">{{ collectFiles }}个文件，总大小: {{ collectSize }}</div>
                </div>
                <div class="metric-row">
                  <div class="metric-label">压缩文件(已被压缩的待上传文件)</div>
                  <div class="metric-value">{{ compressFiles }}个文件，总大小: {{ compressSize }}</div>
                </div>
              </div>
              
              <div class="metric-section terminal-cpe-section">
                <h5><i class="icon-network"></i>终端与CPE信息</h5>

                <!-- 终端总览信息 -->
                <div class="terminal-overview">
                  <div class="overview-grid">
                    <div class="overview-item">
                      <div class="overview-label">终端组ID</div>
                      <div class="overview-value">{{ terminalGroupId }}</div>
                    </div>
                    <div class="overview-item">
                      <div class="overview-label">总连接数</div>
                      <div class="overview-value">{{ totalConnections }}</div>
                    </div>
                    <div class="overview-item">
                      <div class="overview-label">总包速率</div>
                      <div class="overview-value">{{ formatPacketRate(totalPps) }}</div>
                    </div>
                    <div class="overview-item">
                      <div class="overview-label">总流量速率</div>
                      <div class="overview-value">{{ formatNetworkSpeed(totalBps) }}</div>
                    </div>
                  </div>
                </div>

                <!-- CPE状态监控 -->
                <div v-if="offlineCpes.length > 0" class="cpe-status-alert">
                  <div class="status-alert-header">
                    <i class="status-alert-icon">⚠️</i>
                    <span class="status-alert-title">CPE无效告警</span>
                    <span class="status-alert-count">{{ offlineCpes.length }}个CPE无效</span>
                  </div>
                  <div class="offline-cpe-list">
                    <div v-for="cpe in offlineCpes" :key="cpe.bucket" class="offline-cpe-item">
                      <div class="offline-cpe-info">
                        <div class="offline-cpe-name">{{ cpe.name }}</div>
                        <div class="offline-cpe-details">
                          <span class="offline-detail">IP: {{ cpe.ip }}</span>
                          <span class="offline-detail">MAC: {{ cpe.eth }}</span>
                          <span class="offline-detail">上次在线: {{ cpe.lastOnlineTime }}</span>
                        </div>
                      </div>
                      <div class="offline-cpe-status">
                        <span class="status-badge offline">离线</span>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- CPE分组详情 -->
                <div class="cpe-groups">
                  <div class="cpe-groups-header">CPE分组详情</div>
                  <div class="cpe-groups-grid">
                    <div v-for="(bucket, index) in cpeBuckets" :key="index" class="cpe-group-card">
                      <div class="cpe-group-title">{{ getCpeName(bucket.bucket) }}({{ getCpeIp(bucket.bucket) }})</div>
                      <div class="cpe-group-stats">
                        <div class="cpe-stat">
                          <span class="cpe-stat-label">连接数</span>
                          <span class="cpe-stat-value">{{ bucket.conn }}</span>
                        </div>
                        <div class="cpe-stat">
                          <span class="cpe-stat-label">包速率</span>
                          <span class="cpe-stat-value">{{ formatPacketRate(bucket.pps) }}</span>
                        </div>
                        <div class="cpe-stat">
                          <span class="cpe-stat-label">速率</span>
                          <span class="cpe-stat-value">{{ formatNetworkSpeed(bucket.bps) }}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 加载状态 -->
        <div v-if="loading && !metrics" class="loading-container">
          <div class="loading-text">正在加载实时指标数据...</div>
        </div>

        <!-- 错误状态 -->
        <div v-if="!loading && !metrics" class="empty-message">
          <div class="error-icon">⚠️</div>
          <div class="error-title">无法获取指标数据</div>
          <div class="error-details">
            <p>可能的原因：</p>
            <ul>
              <li>设备 {{ identityMac }} 暂无指标数据</li>
              <li>后端服务连接异常</li>
              <li>Elasticsearch服务未启动</li>
            </ul>
          </div>
          <div class="error-actions">
            <button class="btn btn-primary" @click="refreshMetrics" style="margin-right: 10px;">
              <i class="fas fa-sync-alt"></i> 重新加载
            </button>
            <button class="btn btn-secondary" @click="testAPI">
              <i class="fas fa-network-wired"></i> 测试连接
            </button>

          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { terminalApi } from '@/api/services'
import * as echarts from 'echarts'

const route = useRoute()
const router = useRouter()

// 响应式数据
const identityMac = ref(route.params.identityMac)
const metrics = ref(null)
const loading = ref(false)
const autoRefreshTimer = ref(null)
const lastUpdateTime = ref('')
const metricTime = ref('')
const receiveTime = ref('')
const historyTime = ref('') // 新增：用于存储历史时间
const isHistoryMode = ref(false) // 新增：用于控制实时/历史模式

// CPE配置数据
const cpeConfigs = ref([
  { bucket: 0, ip: "**********", eth: "00:50:c6:7c:ad:74" },
  { bucket: 1, ip: "**********", eth: "00:50:c0:24:ed:ec" },
  { bucket: 2, ip: "**********", eth: "00:50:c0:b8:ee:20" },
  { bucket: 3, ip: "**********", eth: "00:50:c0:10:30:d0" }
])

// 图表相关数据
const chartContainer = ref(null)
const chart = ref(null)
const currentTimeRange = ref('最近1小时') // 默认显示最近1小时
const chartUpdateTimer = ref(null)
const metricsHistory = ref([]) // 完整的历史数据
const displayedHistory = ref([]) // 当前显示的数据
const allHistoryData = ref([]) // 保存所有获取到的历史数据

// 时间范围选项
const timeRanges = ref([
  { label: '最近10分钟', value: '最近10分钟' },
  { label: '最近1小时', value: '最近1小时' },
  { label: '最近6小时', value: '最近6小时' },
  { label: '今日全天', value: '今日全天' },
  { label: '最近24小时', value: '最近24小时' }
])

// 变化值计算
const uploadChange = ref(0)
const cpuTempChange = ref(0)
const cpuUsageChange = ref(0)
const memoryChange = ref(0)
const diskChange = ref(0)

// 指标配置相关
const metricsConfigVisible = ref(false)

// 自定义悬浮弹框
const customTooltip = ref({
  visible: false,
  x: 0,
  y: 0,
  time: '',
  date: '',
  metrics: []
})

// 所有可用指标定义
const allMetrics = ref([
  // 基础指标（默认启用）
  {
    key: 'upload',
    label: '上行速率 (MB/s)',
    description: '网络上行实时速率，显示数据上传速度',
    category: '基础指标',
    enabled: true,
    visible: true,
    colorClass: 'legend-upload',
    numberClass: 'value-upload-num',
    yAxisIndex: 0,
    color: '#1890ff'
  },
  {
    key: 'cpuTemp',
    label: 'CPU温度 (°C)',
    description: 'CPU核心温度，监控处理器热量状态',
    category: '基础指标',
    enabled: true,
    visible: true,
    colorClass: 'legend-cpu-temp',
    numberClass: 'value-cpu-temp-num',
    yAxisIndex: 1,
    color: '#ff4d4f'
  },
  {
    key: 'cpuUsage',
    label: 'CPU使用率 (%)',
    description: 'CPU处理器使用率，反映系统负载',
    category: '基础指标',
    enabled: true,
    visible: true,
    colorClass: 'legend-cpu-usage',
    numberClass: 'value-cpu-usage-num',
    yAxisIndex: 2,
    color: '#52c41a'
  },
  {
    key: 'memory',
    label: '内存使用率 (%)',
    description: '系统内存使用率，监控内存占用情况',
    category: '基础指标',
    enabled: true,
    visible: true,
    colorClass: 'legend-memory',
    numberClass: 'value-memory-num',
    yAxisIndex: 2,
    color: '#faad14'
  },
  {
    key: 'disk',
    label: '磁盘使用率 (%)',
    description: '磁盘空间使用率，监控存储空间占用',
    category: '基础指标',
    enabled: false,
    visible: true,
    colorClass: 'legend-disk',
    numberClass: 'value-disk-num',
    yAxisIndex: 2,
    color: '#722ed1'
  },

  // CPU详细信息
  {
    key: 'cpuUserUsage',
    label: 'CPU用户态使用率 (%)',
    description: 'CPU在用户态的使用率，反映用户程序占用',
    category: 'CPU信息',
    enabled: false,
    visible: true,
    colorClass: 'legend-cpu-user',
    numberClass: 'value-cpu-user-num',
    yAxisIndex: 2,
    color: '#13c2c2'
  },
  {
    key: 'cpuSystemUsage',
    label: 'CPU系统态使用率 (%)',
    description: 'CPU在系统态的使用率，反映系统内核占用',
    category: 'CPU信息',
    enabled: false,
    visible: true,
    colorClass: 'legend-cpu-system',
    numberClass: 'value-cpu-system-num',
    yAxisIndex: 2,
    color: '#eb2f96'
  },
  {
    key: 'socTemp',
    label: 'SOC温度 (°C)',
    description: 'SOC芯片温度，监控系统芯片热量',
    category: 'CPU信息',
    enabled: false,
    visible: true,
    colorClass: 'legend-soc-temp',
    numberClass: 'value-soc-temp-num',
    yAxisIndex: 1,
    color: '#f759ab'
  },
  {
    key: 'gpuTemp',
    label: 'GPU温度 (°C)',
    description: 'GPU芯片温度，监控图形处理器热量',
    category: 'CPU信息',
    enabled: false,
    visible: true,
    colorClass: 'legend-gpu-temp',
    numberClass: 'value-gpu-temp-num',
    yAxisIndex: 1,
    color: '#ff7a45'
  },

  // 内存详细信息
  {
    key: 'memoryUsed',
    label: '已用内存 (GB)',
    description: '系统已使用的内存大小',
    category: '内存信息',
    enabled: false,
    visible: true,
    colorClass: 'legend-memory-used',
    numberClass: 'value-memory-used-num',
    yAxisIndex: 3,
    color: '#722ed1'
  },
  {
    key: 'memoryAvailable',
    label: '可用内存 (GB)',
    description: '系统可用的内存大小',
    category: '内存信息',
    enabled: false,
    visible: true,
    colorClass: 'legend-memory-available',
    numberClass: 'value-memory-available-num',
    yAxisIndex: 3,
    color: '#52c41a'
  },

  // 通用存储健康信息（优先使用外置存储）
  {
    key: 'diskHealthTemp',
    label: '硬盘温度 (°C)',
    description: '硬盘设备温度，优先显示外置存储，无外置时显示内置存储',
    category: '存储信息',
    enabled: false,
    visible: true,
    colorClass: 'legend-disk-temp',
    numberClass: 'value-disk-temp-num',
    yAxisIndex: 1,
    color: '#fa8c16'
  },
  {
    key: 'diskLifeUsed',
    label: '硬盘已使用寿命 (%)',
    description: '硬盘寿命使用百分比，优先显示外置存储，无外置时显示内置存储',
    category: '存储信息',
    enabled: false,
    visible: true,
    colorClass: 'legend-disk-life',
    numberClass: 'value-disk-life-num',
    yAxisIndex: 2,
    color: '#a0d911'
  },
  {
    key: 'diskTotalWritten',
    label: '总写入量 (TB)',
    description: '硬盘总写入数据量(TBW)，优先显示外置存储，无外置时显示内置存储',
    category: '存储信息',
    enabled: false,
    visible: true,
    colorClass: 'legend-disk-written',
    numberClass: 'value-disk-written-num',
    yAxisIndex: 4,
    color: '#d3adf7'
  },

  // 内置存储健康信息
  {
    key: 'internalDiskTemp',
    label: '内置硬盘温度 (°C)',
    description: '内置硬盘设备温度，监控内置存储设备热量',
    category: '存储信息',
    enabled: false,
    visible: true,
    colorClass: 'legend-internal-disk-temp',
    numberClass: 'value-internal-disk-temp-num',
    yAxisIndex: 1,
    color: '#fa8c16'
  },
  {
    key: 'internalDiskLifeUsed',
    label: '内置硬盘已使用寿命 (%)',
    description: '内置硬盘寿命使用百分比，监控设备老化程度',
    category: '存储信息',
    enabled: false,
    visible: true,
    colorClass: 'legend-internal-disk-life',
    numberClass: 'value-internal-disk-life-num',
    yAxisIndex: 2,
    color: '#a0d911'
  },
  {
    key: 'internalDiskTotalWritten',
    label: '内置硬盘总写入量 (TB)',
    description: '内置硬盘总写入数据量(TBW)，监控写入负载',
    category: '存储信息',
    enabled: false,
    visible: true,
    colorClass: 'legend-internal-disk-written',
    numberClass: 'value-internal-disk-written-num',
    yAxisIndex: 4,
    color: '#d3adf7'
  },

  // 外置存储健康信息
  {
    key: 'externalDiskTemp',
    label: '外置硬盘温度 (°C)',
    description: '外置硬盘设备温度，监控外置存储设备热量',
    category: '存储信息',
    enabled: false,
    visible: true,
    colorClass: 'legend-external-disk-temp',
    numberClass: 'value-external-disk-temp-num',
    yAxisIndex: 1,
    color: '#ff85c0'
  },
  {
    key: 'externalDiskLifeUsed',
    label: '外置硬盘已使用寿命 (%)',
    description: '外置硬盘寿命使用百分比，监控设备老化程度',
    category: '存储信息',
    enabled: false,
    visible: true,
    colorClass: 'legend-external-disk-life',
    numberClass: 'value-external-disk-life-num',
    yAxisIndex: 2,
    color: '#87e8de'
  },
  {
    key: 'externalDiskTotalWritten',
    label: '外置硬盘总写入量 (TB)',
    description: '外置硬盘总写入数据量(TBW)，监控写入负载',
    category: '存储信息',
    enabled: false,
    visible: true,
    colorClass: 'legend-external-disk-written',
    numberClass: 'value-external-disk-written-num',
    yAxisIndex: 4,
    color: '#ffadd6'
  },

  // 数据文件信息
  {
    key: 'collectFileCount',
    label: '原始文件数量',
    description: '原始数据文件的数量',
    category: '数据文件',
    enabled: false,
    visible: true,
    colorClass: 'legend-collect-count',
    numberClass: 'value-collect-count-num',
    yAxisIndex: 5,
    color: '#40a9ff'
  },
  {
    key: 'compressFileCount',
    label: '压缩文件数量',
    description: '压缩数据文件的数量',
    category: '数据文件',
    enabled: false,
    visible: true,
    colorClass: 'legend-compress-count',
    numberClass: 'value-compress-count-num',
    yAxisIndex: 5,
    color: '#36cfc9'
  },

  // 终端与CPE信息
  {
    key: 'totalConnections',
    label: '总连接数',
    description: '终端总连接数量',
    category: '终端与CPE信息',
    enabled: false,
    visible: true,
    colorClass: 'legend-connections',
    numberClass: 'value-connections-num',
    yAxisIndex: 5,
    color: '#95de64'
  },
  {
    key: 'totalPps',
    label: '总包速率 (pps)',
    description: '终端总包处理速率',
    category: '终端与CPE信息',
    enabled: false,
    visible: true,
    colorClass: 'legend-pps',
    numberClass: 'value-pps-num',
    yAxisIndex: 6,
    color: '#ffc069'
  }
])

// 初始化
onMounted(async () => {
  if (!identityMac.value) {
    ElMessage.error('缺少设备MAC地址参数')
    router.push('/terminals')
    return
  }

  // 检查是否是历史模式
  isHistoryMode.value = !!route.query.historyTime
  if (isHistoryMode.value) {
    historyTime.value = route.query.historyTime
    await loadMetrics()
  } else {
    // 实时模式：根据默认时间范围获取历史数据，再获取实时数据
    try {
      console.log(`开始获取${currentTimeRange.value}历史数据...`)
      await fetchHistoryDataByTimeRange(currentTimeRange.value)
      console.log('历史数据获取完成，开始获取最新数据...')
      await loadMetrics()
      console.log('最新数据获取完成，启动自动刷新...')
      startAutoRefresh()
    } catch (error) {
      console.error('初始化数据获取失败:', error)
      ElMessage.error('初始化失败: ' + error.message)
    }
  }

  // 加载用户指标配置
  loadMetricsConfig()

  // 初始化图表
  await nextTick()
  initChart()
})

// 清理定时器
onUnmounted(() => {
  if (autoRefreshTimer.value) {
    clearInterval(autoRefreshTimer.value)
  }
  if (chartUpdateTimer.value) {
    clearInterval(chartUpdateTimer.value)
  }
  if (chart.value) {
    chart.value.dispose()
  }
})

// 计算属性
const showDebugButton = computed(() => {
  return import.meta.env.VITE_DEBUG === 'true'
})

const breadcrumbText = computed(() => {
  const from = route.query.from
  return from === 'alerts' ? '站内告警' : '终端管理'
})

const uptime = computed(() => {
  if (!metrics.value) return '0分钟'

  // 如果有后端提供的uptime数据（单位：秒）
  if (metrics.value.uptime && typeof metrics.value.uptime === 'number') {
    const totalSeconds = Math.floor(metrics.value.uptime)
    const days = Math.floor(totalSeconds / (24 * 3600))
    const hours = Math.floor((totalSeconds % (24 * 3600)) / 3600)
    const minutes = Math.floor((totalSeconds % 3600) / 60)

    if (days > 0) {
      return `${days}天${hours}小时${minutes}分钟`
    } else if (hours > 0) {
      return `${hours}小时${minutes}分钟`
    } else {
      return `${minutes}分钟`
    }
  }

  // 如果没有数据，返回未知
  return '未知'
})

const socTemperature = computed(() => {
  if (!metrics.value) return 0
  // 如果有原始后端数据，使用temperatures中的soc-thermal
  if (metrics.value.temperatures && metrics.value.temperatures['soc-thermal']) {
    return metrics.value.temperatures['soc-thermal']
  }
  // 如果没有soc-thermal数据，返回0或使用CPU温度
  return metrics.value.cpu?.temperature || 0
})

const gpuTemperature = computed(() => {
  if (!metrics.value) return 0
  // 如果有原始后端数据，使用temperatures中的gpu-thermal
  if (metrics.value.temperatures && metrics.value.temperatures['gpu-thermal']) {
    return metrics.value.temperatures['gpu-thermal']
  }
  // 如果没有gpu-thermal数据，返回0或使用CPU温度
  return metrics.value.cpu?.temperature || 0
})

const cpuUserUsage = computed(() => {
  if (!metrics.value) return 0
  // 如果有原始后端数据，使用cpuUsage中的user
  if (metrics.value.cpuUsage && metrics.value.cpuUsage.user) {
    return parseFloat(metrics.value.cpuUsage.user)
  }
  // 如果没有详细的CPU使用率数据，返回0
  return 0
})

const cpuSystemUsage = computed(() => {
  if (!metrics.value) return 0
  // 如果有原始后端数据，使用cpuUsage中的sys
  if (metrics.value.cpuUsage && metrics.value.cpuUsage.sys) {
    return parseFloat(metrics.value.cpuUsage.sys)
  }
  // 如果没有详细的CPU使用率数据，返回0
  return 0
})

const cpuIdleUsage = computed(() => {
  if (!metrics.value) return 100
  // 如果有原始后端数据，使用cpuUsage中的idle
  if (metrics.value.cpuUsage && metrics.value.cpuUsage.idle) {
    return parseFloat(metrics.value.cpuUsage.idle)
  }
  return 100 - metrics.value.cpu.usage
})

const cacheMemory = computed(() => {
  if (!metrics.value) return '0MB'
  // 如果有后端提供的memoryUsage数据
  if (metrics.value.memoryUsage && metrics.value.memoryUsage.cached) {
    return formatBytes(metrics.value.memoryUsage.cached, 'MB')
  }
  // 如果没有缓存数据，返回0
  return '0MB'
})

const bufferMemory = computed(() => {
  if (!metrics.value) return '0MB'
  // 如果有后端提供的memoryUsage数据
  if (metrics.value.memoryUsage && metrics.value.memoryUsage.buffers) {
    return formatBytes(metrics.value.memoryUsage.buffers, 'MB')
  }
  // 如果没有缓冲区数据，返回0
  return '0MB'
})

const collectFiles = computed(() => {
  if (metrics.value && metrics.value.cdata && typeof metrics.value.cdata.count === 'number') {
    return metrics.value.cdata.count
  }
  return 0
})

const collectSize = computed(() => {
  if (metrics.value && metrics.value.cdata) {
    // 支持两种可能的字段名格式
    const size = metrics.value.cdata.size || metrics.value.cdata['size:']
    if (typeof size === 'number') {
      return formatBytes(size)
    }
  }
  return '0 B'
})

const compressFiles = computed(() => {
  if (metrics.value && metrics.value.zdata && typeof metrics.value.zdata.count === 'number') {
    return metrics.value.zdata.count
  }
  return 0
})

const compressSize = computed(() => {
  if (metrics.value && metrics.value.zdata) {
    // 支持两种可能的字段名格式
    const size = metrics.value.zdata.size || metrics.value.zdata['size:']
    if (typeof size === 'number') {
      return formatBytes(size)
    }
  }
  return '0 B'
})

// 网络流量相关计算属性
const groupBpsValue = computed(() => {
  if (metrics.value && metrics.value.groupBps) {
    return metrics.value.groupBps
  }
  return 0
})

// 存储设备相关计算属性
const internalStorage = computed(() => {
  if (metrics.value && metrics.value.diskUsage && metrics.value.diskUsage[0]) {
    return metrics.value.diskUsage[0]
  }
  return {
    device: '/dev/mmcblk0p6',
    total: 0,
    used: 0,
    free: 0,
    percent: 0,
    io_rate: { 'MBr/s': 0, 'MBw/s': 0, 'read/s': 0, 'write/s': 0 }
  }
})

const externalStorage = computed(() => {
  if (metrics.value && metrics.value.diskUsage && metrics.value.diskUsage[1]) {
    return metrics.value.diskUsage[1]
  }
  return null
})

// 检查是否有外置存储
const hasExternalStorage = computed(() => {
  return externalStorage.value !== null
})

// 存储信息展开状态
const storageExpanded = ref(false)

// 硬盘健康情况相关计算属性
const internalStorageSmartmon = computed(() => {
  if (metrics.value && metrics.value.diskUsage && metrics.value.diskUsage[0] && metrics.value.diskUsage[0].smartmon) {
    const smartmon = metrics.value.diskUsage[0].smartmon
    // 检查smartmon是否为空对象
    return Object.keys(smartmon).length > 0 ? smartmon : null
  }
  return null
})

const externalStorageSmartmon = computed(() => {
  if (metrics.value && metrics.value.diskUsage && metrics.value.diskUsage[1] && metrics.value.diskUsage[1].smartmon) {
    const smartmon = metrics.value.diskUsage[1].smartmon
    // 检查smartmon是否为空对象
    return Object.keys(smartmon).length > 0 ? smartmon : null
  }
  return null
})

// 硬盘健康情况格式化函数
const formatDiskTemperature = (temp) => {
  return temp ? `${temp}°C` : '未知'
}

const formatDiskLifeUsed = (percentage) => {
  return percentage !== undefined ? `${percentage}%` : '未知'
}

const formatTotalBytesWritten = (dataUnitsWritten) => {
  if (!dataUnitsWritten) return '0 TB'
  // data_units_written * 512 / 1000 / 1000 / 1000 单位为GB
  const gbWritten = (dataUnitsWritten * 512) / (1000 * 1000 * 1000)
  return `${gbWritten.toFixed(2)} TB`
}

// 辅助函数：从数据中提取存储健康信息
const getPreferredDiskTemp = (data) => {
  // 优先使用外置存储数据
  if (data.diskUsage && data.diskUsage[1] && data.diskUsage[1].smartmon && data.diskUsage[1].smartmon.temperature) {
    return data.diskUsage[1].smartmon.temperature
  }
  // 如果没有外置存储，使用内置存储数据
  if (data.diskUsage && data.diskUsage[0] && data.diskUsage[0].smartmon && data.diskUsage[0].smartmon.temperature) {
    return data.diskUsage[0].smartmon.temperature
  }
  return 45 // 默认值
}

const getPreferredDiskLifeUsed = (data) => {
  // 优先使用外置存储数据
  if (data.diskUsage && data.diskUsage[1] && data.diskUsage[1].smartmon && data.diskUsage[1].smartmon.percentage_used !== undefined) {
    return data.diskUsage[1].smartmon.percentage_used
  }
  // 如果没有外置存储，使用内置存储数据
  if (data.diskUsage && data.diskUsage[0] && data.diskUsage[0].smartmon && data.diskUsage[0].smartmon.percentage_used !== undefined) {
    return data.diskUsage[0].smartmon.percentage_used
  }
  return 15 // 默认值
}

const getPreferredDiskTotalWritten = (data) => {
  // 优先使用外置存储数据
  if (data.diskUsage && data.diskUsage[1] && data.diskUsage[1].smartmon && data.diskUsage[1].smartmon.data_units_written) {
    return (data.diskUsage[1].smartmon.data_units_written * 512) / (1000 * 1000 * 1000 * 1000) // 转换为 TB
  }
  // 如果没有外置存储，使用内置存储数据
  if (data.diskUsage && data.diskUsage[0] && data.diskUsage[0].smartmon && data.diskUsage[0].smartmon.data_units_written) {
    return (data.diskUsage[0].smartmon.data_units_written * 512) / (1000 * 1000 * 1000 * 1000) // 转换为 TB
  }
  return 100 // 默认值
}

const getInternalDiskTemp = (data) => {
  if (data.diskUsage && data.diskUsage[0] && data.diskUsage[0].smartmon && data.diskUsage[0].smartmon.temperature) {
    return data.diskUsage[0].smartmon.temperature
  }
  return 42 // 默认值
}

const getInternalDiskLifeUsed = (data) => {
  if (data.diskUsage && data.diskUsage[0] && data.diskUsage[0].smartmon && data.diskUsage[0].smartmon.percentage_used !== undefined) {
    return data.diskUsage[0].smartmon.percentage_used
  }
  return 12 // 默认值
}

const getInternalDiskTotalWritten = (data) => {
  if (data.diskUsage && data.diskUsage[0] && data.diskUsage[0].smartmon && data.diskUsage[0].smartmon.data_units_written) {
    return (data.diskUsage[0].smartmon.data_units_written * 512) / (1000 * 1000 * 1000 * 1000) // 转换为 TB
  }
  return 80 // 默认值
}

const getExternalDiskTemp = (data) => {
  if (data.diskUsage && data.diskUsage[1] && data.diskUsage[1].smartmon && data.diskUsage[1].smartmon.temperature) {
    return data.diskUsage[1].smartmon.temperature
  }
  return 48 // 默认值
}

const getExternalDiskLifeUsed = (data) => {
  if (data.diskUsage && data.diskUsage[1] && data.diskUsage[1].smartmon && data.diskUsage[1].smartmon.percentage_used !== undefined) {
    return data.diskUsage[1].smartmon.percentage_used
  }
  return 18 // 默认值
}

const getExternalDiskTotalWritten = (data) => {
  if (data.diskUsage && data.diskUsage[1] && data.diskUsage[1].smartmon && data.diskUsage[1].smartmon.data_units_written) {
    return (data.diskUsage[1].smartmon.data_units_written * 512) / (1000 * 1000 * 1000 * 1000) // 转换为 TB
  }
  return 120 // 默认值
}

// 获取优先存储的smartmon数据（优先外置存储）
const getPreferredStorageSmartmon = () => {
  console.log('getPreferredStorageSmartmon called')
  console.log('externalStorageSmartmon.value:', externalStorageSmartmon.value)
  console.log('internalStorageSmartmon.value:', internalStorageSmartmon.value)

  // 优先使用外置存储的数据
  if (externalStorageSmartmon.value && Object.keys(externalStorageSmartmon.value).length > 0) {
    console.log('Using external storage smartmon:', externalStorageSmartmon.value)
    return externalStorageSmartmon.value
  }
  // 如果外置存储没有数据，使用内置存储的数据
  if (internalStorageSmartmon.value && Object.keys(internalStorageSmartmon.value).length > 0) {
    console.log('Using internal storage smartmon:', internalStorageSmartmon.value)
    return internalStorageSmartmon.value
  }
  console.log('No storage smartmon data available')
  return null
}

const getDiskHealthColor = (percentage) => {
  if (percentage >= 80) return '#F56C6C' // 红色：寿命使用超过80%
  if (percentage >= 60) return '#E6A23C' // 橙色：寿命使用60-80%
  return '#67C23A' // 绿色：寿命使用低于60%
}

// 切换存储信息展开状态
const toggleStorageExpanded = () => {
  storageExpanded.value = !storageExpanded.value
}

// 指标配置相关计算属性
const visibleMetrics = computed(() => {
  return allMetrics.value.filter(metric => metric.enabled)
})

const enabledMetrics = computed(() => {
  return allMetrics.value.filter(metric => metric.enabled)
})

const enabledMetricsCount = computed(() => {
  return allMetrics.value.filter(metric => metric.enabled).length
})

// 指标分类
const metricCategories = computed(() => {
  const categories = [...new Set(allMetrics.value.map(metric => metric.category))]
  return categories
})

// 按分类获取指标
const getMetricsByCategory = (category) => {
  return allMetrics.value.filter(metric => metric.category === category)
}

// 获取分类中启用的指标数量
const getCategoryEnabledCount = (category) => {
  return allMetrics.value.filter(metric => metric.category === category && metric.enabled).length
}

// 获取分类中总指标数量
const getCategoryTotalCount = (category) => {
  return allMetrics.value.filter(metric => metric.category === category).length
}

// 指标配置相关函数
const showMetricsConfig = () => {
  metricsConfigVisible.value = true
}

const hideMetricsConfig = () => {
  metricsConfigVisible.value = false
}

const updateMetricConfig = (event, metricKey) => {
  const enabledCount = allMetrics.value.filter(m => m.enabled).length

  // 如果是要启用指标且已经达到4个限制
  if (event.target.checked && enabledCount >= 4) {
    event.preventDefault()
    event.target.checked = false
    ElMessage.warning('页面最多展示4个指标趋势图，请先取消其他指标后再选择')
    return
  }

  // 实时更新图表
  updateChartData()
}

const toggleMetricVisibility = (metricKey) => {
  const metric = allMetrics.value.find(m => m.key === metricKey)
  if (metric) {
    metric.visible = !metric.visible
    updateChartData()
  }
}

const resetMetricsConfig = () => {
  // 重置为默认配置：只启用前3个基础指标
  const defaultEnabledKeys = ['upload', 'cpuTemp', 'cpuUsage']

  allMetrics.value.forEach(metric => {
    metric.enabled = defaultEnabledKeys.includes(metric.key)
    metric.visible = true
  })
  updateChartData()
}

const applyMetricsConfig = () => {
  // 保存配置到本地存储
  const config = allMetrics.value.map(metric => ({
    key: metric.key,
    enabled: metric.enabled,
    visible: metric.visible
  }))
  localStorage.setItem(`metrics-config-${identityMac.value}`, JSON.stringify(config))

  updateChartData()
  hideMetricsConfig()
}

// 获取指标当前值
const getMetricCurrentValue = (metricKey) => {
  if (!metrics.value) return '0'

  switch (metricKey) {
    // 基础指标
    case 'upload':
      return formatNetworkSpeed(groupBpsValue.value)
    case 'cpuTemp':
      return formatTemperature(metrics.value?.cpu?.temperature || 0)
    case 'cpuUsage':
      return formatPercentage(metrics.value?.cpu?.usage || 0)
    case 'memory':
      return formatPercentage(memoryPercent.value)
    case 'disk':
      return formatPercentage(metrics.value?.disk?.usage || 0)

    // CPU详细信息
    case 'cpuUserUsage':
      return formatPercentage(cpuUserUsage.value)
    case 'cpuSystemUsage':
      return formatPercentage(cpuSystemUsage.value)
    case 'socTemp':
      return formatTemperature(socTemperature.value)
    case 'gpuTemp':
      return formatTemperature(gpuTemperature.value)

    // 内存详细信息
    case 'memoryUsed':
      return formatBytes(memoryUsed.value)
    case 'memoryAvailable':
      return formatBytes(memoryAvailable.value)

    // 通用存储健康信息（优先使用外置存储）
    case 'diskHealthTemp':
      const preferredSmartmon = getPreferredStorageSmartmon()
      console.log('diskHealthTemp - preferredSmartmon:', preferredSmartmon)
      return formatTemperature(preferredSmartmon?.temperature || 0)
    case 'diskLifeUsed':
      return formatPercentage(getPreferredStorageSmartmon()?.percentage_used || 0)
    case 'diskTotalWritten':
      return formatTotalBytesWritten(getPreferredStorageSmartmon()?.data_units_written || 0)

    // 内置存储健康信息
    case 'internalDiskTemp':
      return formatTemperature(internalStorageSmartmon.value?.temperature || 0)
    case 'internalDiskLifeUsed':
      return formatPercentage(internalStorageSmartmon.value?.percentage_used || 0)
    case 'internalDiskTotalWritten':
      return formatTotalBytesWritten(internalStorageSmartmon.value?.data_units_written || 0)

    // 外置存储健康信息
    case 'externalDiskTemp':
      console.log('externalDiskTemp - externalStorageSmartmon.value:', externalStorageSmartmon.value)
      return formatTemperature(externalStorageSmartmon.value?.temperature || 0)
    case 'externalDiskLifeUsed':
      return formatPercentage(externalStorageSmartmon.value?.percentage_used || 0)
    case 'externalDiskTotalWritten':
      return formatTotalBytesWritten(externalStorageSmartmon.value?.data_units_written || 0)

    // 数据文件信息
    case 'collectFileCount':
      return collectFiles.value.toString()
    case 'compressFileCount':
      return compressFiles.value.toString()

    // 终端与CPE信息
    case 'totalConnections':
      return totalConnections.value.toString()
    case 'totalPps':
      return formatPacketRate(totalPps.value)

    default:
      return '0'
  }
}

// 获取指标变化值
const getMetricChange = (metricKey) => {
  // 对于新增的指标，暂时返回0，因为我们没有为每个指标单独计算变化值
  // 可以根据需要扩展变化值计算
  switch (metricKey) {
    case 'upload':
      return uploadChange.value
    case 'cpuTemp':
      return cpuTempChange.value
    case 'cpuUsage':
      return cpuUsageChange.value
    case 'memory':
      return memoryChange.value
    case 'disk':
      return diskChange.value
    default:
      return 0
  }
}

// 终端信息相关计算属性
const terminalGroupId = computed(() => {
  if (metrics.value && metrics.value.groupUsage && metrics.value.groupUsage.group_id) {
    return metrics.value.groupUsage.group_id
  }
  return 'N/A'
})

const totalConnections = computed(() => {
  if (metrics.value && metrics.value.groupUsage && metrics.value.groupUsage.buckets) {
    return metrics.value.groupUsage.buckets.reduce((total, bucket) => total + bucket.conn, 0)
  }
  return 0
})

const totalPps = computed(() => {
  if (metrics.value && metrics.value.groupUsage && metrics.value.groupUsage.pps_total) {
    return metrics.value.groupUsage.pps_total
  }
  return 0
})

const totalBps = computed(() => {
  if (metrics.value && metrics.value.groupUsage && metrics.value.groupUsage.bps_total) {
    return metrics.value.groupUsage.bps_total
  }
  return 0
})

const cpeBuckets = computed(() => {
  if (metrics.value && metrics.value.groupUsage && metrics.value.groupUsage.buckets) {
    return metrics.value.groupUsage.buckets
  }
  return []
})

// CPE离线检测
const offlineCpes = computed(() => {
  const onlineBuckets = cpeBuckets.value.map(bucket => bucket.bucket)
  const offlineCpeList = []

  cpeConfigs.value.forEach(config => {
    if (!onlineBuckets.includes(config.bucket)) {
      offlineCpeList.push({
        bucket: config.bucket,
        name: `CPE-0${config.bucket + 1}`,
        ip: config.ip,
        eth: config.eth,
        lastOnlineTime: getLastOnlineTime()
      })
    }
  })

  return offlineCpeList
})

// 获取CPE名称
const getCpeName = (bucket) => {
  return `CPE-0${bucket + 1}`
}

// 获取CPE IP地址
const getCpeIp = (bucket) => {
  const config = cpeConfigs.value.find(c => c.bucket === bucket)
  return config ? config.ip : 'Unknown'
}

// 获取上次在线时间
const getLastOnlineTime = () => {
  if (receiveTime.value) {
    return receiveTime.value
  }
  return '未知'
}

// 内存相关计算属性
const memoryTotal = computed(() => {
  if (metrics.value && metrics.value.memoryUsage && metrics.value.memoryUsage.total) {
    return metrics.value.memoryUsage.total
  }
  return metrics.value?.memory?.total || 0
})

const memoryUsed = computed(() => {
  if (metrics.value && metrics.value.memoryUsage && metrics.value.memoryUsage.used) {
    return metrics.value.memoryUsage.used
  }
  return metrics.value?.memory?.used || 0
})

const memoryAvailable = computed(() => {
  if (metrics.value && metrics.value.memoryUsage && metrics.value.memoryUsage.available) {
    return metrics.value.memoryUsage.available
  }
  return metrics.value?.memory?.available || 0
})

const memoryPercent = computed(() => {
  if (metrics.value && metrics.value.memoryUsage && metrics.value.memoryUsage.percent) {
    return metrics.value.memoryUsage.percent
  }
  return metrics.value?.memory?.usage || 0
})

const memoryFree = computed(() => {
  if (metrics.value && metrics.value.memoryUsage && metrics.value.memoryUsage.free) {
    return metrics.value.memoryUsage.free
  }
  return 0
})

const memoryActive = computed(() => {
  if (metrics.value && metrics.value.memoryUsage && metrics.value.memoryUsage.active) {
    return metrics.value.memoryUsage.active
  }
  return 0
})

const memoryInactive = computed(() => {
  if (metrics.value && metrics.value.memoryUsage && metrics.value.memoryUsage.inactive) {
    return metrics.value.memoryUsage.inactive
  }
  return 0
})

const memoryBuffers = computed(() => {
  if (metrics.value && metrics.value.memoryUsage && metrics.value.memoryUsage.buffers) {
    return metrics.value.memoryUsage.buffers
  }
  return 0
})

const memoryCached = computed(() => {
  if (metrics.value && metrics.value.memoryUsage && metrics.value.memoryUsage.cached) {
    return metrics.value.memoryUsage.cached
  }
  return 0
})

const memoryShared = computed(() => {
  if (metrics.value && metrics.value.memoryUsage && metrics.value.memoryUsage.shared) {
    return metrics.value.memoryUsage.shared
  }
  return 0
})

const memorySlab = computed(() => {
  if (metrics.value && metrics.value.memoryUsage && metrics.value.memoryUsage.slab) {
    return metrics.value.memoryUsage.slab
  }
  return 0
})

// 开始自动刷新
const startAutoRefresh = () => {
  // 历史模式不需要自动刷新
  if (isHistoryMode.value) return

  if (autoRefreshTimer.value) {
    clearInterval(autoRefreshTimer.value)
  }

  autoRefreshTimer.value = setInterval(() => {
    loadLatestMetrics()
  }, 10000) // 每10秒刷新一次
}

// 手动刷新指标
const refreshMetrics = async () => {
  await loadMetrics()
}

// 加载最新指标数据并添加到历史记录
const loadLatestMetrics = async () => {
  try {
    console.log('获取最新指标数据...', identityMac.value)

    // 调用API获取最新数据
    const response = await terminalApi.getRealTimeMetrics(identityMac.value)
    console.log('最新指标API响应:', response)

    if (response && (response.data || response)) {
      const data = response.data || response

      // 转换ES数据格式
      const processedData = convertBackendMetricsToFrontend(data)
      console.log('处理后的数据:', {
        metricTime: processedData.metricTime,
        receiveTime: processedData.receiveTime,
        identityMac: processedData.identityMac
      })

      // 更新当前指标
      metrics.value = processedData

      // 更新时间信息
      const now = new Date()
      if (processedData && processedData.metricTime) {
        metricTime.value = formatDateTime(processedData.metricTime)
        receiveTime.value = formatDateTime(processedData.receiveTime || processedData.metricTime)
      } else {
        const timeString = formatDateTime(now)
        metricTime.value = timeString
        receiveTime.value = timeString
      }
      lastUpdateTime.value = now.toLocaleString('zh-CN')

      // 添加到历史记录用于图表显示（会自动检查重复数据）
      const historyLengthBefore = metricsHistory.value.length
      addToHistory(processedData)
      const historyLengthAfter = metricsHistory.value.length

      if (historyLengthAfter > historyLengthBefore) {
        console.log('最新指标数据更新成功，已添加到历史记录')
        // 只有在真正添加了新数据时才更新图表
        updateDisplayedHistory()
        // 更新图表显示
        if (chart.value) {
          updateChartData()
        }
      } else {
        console.log('最新指标数据为重复数据，未添加到历史记录')
      }
    } else {
      console.warn('最新指标API响应数据为空')
    }
  } catch (error) {
    console.error('获取最新指标数据失败:', error)
    ElMessage.warning('获取最新数据失败: ' + error.message)
  }
}

// 添加数据到历史记录
const addToHistory = (data) => {
  if (!data) return

  const now = new Date()
  const receiveTime = data.receiveTime ? new Date(data.receiveTime) : (data.metricTime ? new Date(data.metricTime) : now)

  // 检查是否为重复数据：比较时间戳
  if (metricsHistory.value.length > 0) {
    const lastHistoryPoint = metricsHistory.value[metricsHistory.value.length - 1]

    // 比较接收时间和指标时间（字符串比较）
    const lastReceiveTime = lastHistoryPoint.receiveTime
    const lastMetricTime = lastHistoryPoint.metricTime
    const currentReceiveTime = data.receiveTime
    const currentMetricTime = data.metricTime

    console.log('重复数据检测:', {
      lastReceiveTime,
      lastMetricTime,
      currentReceiveTime,
      currentMetricTime
    })

    // 如果接收时间和指标时间都相同，则认为是重复数据
    if (lastReceiveTime === currentReceiveTime && lastMetricTime === currentMetricTime) {
      console.log('检测到重复数据（字符串比较），跳过添加到历史记录')
      return // 跳过重复数据
    }

    // 额外检查：如果时间戳相同（防止时间格式不一致的情况）
    const lastTime = lastHistoryPoint.time.getTime()
    const currentTime = receiveTime.getTime()
    if (lastTime === currentTime) {
      console.log('检测到相同时间戳的重复数据，跳过添加:', {
        lastTime: new Date(lastTime).toISOString(),
        currentTime: new Date(currentTime).toISOString()
      })
      return // 跳过重复数据
    }

    // 如果只有指标时间相同（更宽松的检查）
    if (currentMetricTime && lastMetricTime === currentMetricTime) {
      console.log('检测到相同指标时间的重复数据，跳过添加:', {
        metricTime: currentMetricTime
      })
      return // 跳过重复数据
    }
  }

  // 创建历史数据点
  const historyPoint = {
    time: receiveTime,
    receiveTime: data.receiveTime, // 保存原始receiveTime字符串
    metricTime: data.metricTime,   // 保存原始metricTime字符串
    rawData: data, // 保存原始数据用于tooltip显示

    // 基础指标
    upload: (data.groupBps || 0) / (1024 * 1024), // 转换为 MB/s
    cpuTemp: data.cpuTemp || data.cpu?.temperature || 0,
    cpuUsage: data.cpuPercent || data.cpu?.usage || 0,
    memory: data.memoryPercent || data.memory?.usage || 0,
    disk: (data.diskUsage && data.diskUsage[0]) ? data.diskUsage[0].percent : (data.disk?.usage || 0),

    // CPU详细信息
    cpuUserUsage: data.cpuUsage ? parseFloat(data.cpuUsage.user || 0) : 0,
    cpuSystemUsage: data.cpuUsage ? parseFloat(data.cpuUsage.sys || 0) : 0,
    socTemp: data.temperatures ? (data.temperatures['soc-thermal'] || 0) : 0,
    gpuTemp: data.temperatures ? (data.temperatures['gpu-thermal'] || 0) : 0,

    // 内存详细信息
    memoryUsed: data.memoryUsage ? (data.memoryUsage.used / (1024 * 1024 * 1024)) : 0, // 转换为 GB
    memoryAvailable: data.memoryUsage ? (data.memoryUsage.available / (1024 * 1024 * 1024)) : 0, // 转换为 GB

    // 存储健康信息（从实际数据中获取）
    diskHealthTemp: getPreferredDiskTemp(data),
    diskLifeUsed: getPreferredDiskLifeUsed(data),
    diskTotalWritten: getPreferredDiskTotalWritten(data),

    // 内置存储
    internalDiskTemp: getInternalDiskTemp(data),
    internalDiskLifeUsed: getInternalDiskLifeUsed(data),
    internalDiskTotalWritten: getInternalDiskTotalWritten(data),

    // 外置存储
    externalDiskTemp: getExternalDiskTemp(data),
    externalDiskLifeUsed: getExternalDiskLifeUsed(data),
    externalDiskTotalWritten: getExternalDiskTotalWritten(data),

    // 数据文件信息
    collectFileCount: data.cdata ? data.cdata.count : 0,
    compressFileCount: data.zdata ? data.zdata.count : 0,

    // 终端与CPE信息
    totalConnections: data.groupUsage ? Object.keys(data.groupUsage).length : 0,
    totalPps: data.groupBps || 0
  }

  // 添加到历史记录
  metricsHistory.value.push(historyPoint)

  // 限制历史记录数量（保持更多数据用于拖动查看）
  const maxTotalPoints = 1000 // 保存更多历史数据
  if (metricsHistory.value.length > maxTotalPoints) {
    metricsHistory.value = metricsHistory.value.slice(-maxTotalPoints)
  }

  console.log(`添加历史数据点，当前历史记录数量: ${metricsHistory.value.length}`)
}

// 加载指标数据
const loadMetrics = async () => {
  loading.value = true
  try {
    let response

    if (isHistoryMode.value) {
      // 历史模式：获取指定时间的历史数据
      response = await terminalApi.getHistoryMetrics(identityMac.value, {
        timestamp: historyTime.value
      })
    } else {
      // 实时模式：获取实时数据
      response = await terminalApi.getRealTimeMetrics(identityMac.value)
    }

    // 处理后端返回的数据格式
    let metricsData

    // 兼容不同的响应结构
    if (response.data && response.data.metricTime) {
      // Mock数据或包装的响应结构
      metricsData = response.data
    } else if (response.metricTime) {
      // 真实API直接返回的指标数据
      metricsData = response
    } else if (response.data) {
      // 其他包装结构
      metricsData = response.data
    } else {
      metricsData = response
    }

    console.log('Raw API Response:', response)
    console.log('Processed Metrics Data:', metricsData)

    // 如果是后端真实数据，需要转换格式
    if (metricsData && metricsData.metricTime) {
      // 后端返回的是TerminalMetricDocument格式，需要转换
      metricsData = convertBackendMetricsToFrontend(metricsData)
      console.log('Converted Metrics Data:', metricsData)
    } else if (!metricsData) {
      console.error('No metrics data received')
      ElMessage.error('未获取到指标数据')
      return
    }

    metrics.value = metricsData

    // 更新时间信息
    if (isHistoryMode.value) {
      // 历史模式：使用历史时间
      metricTime.value = historyTime.value
      receiveTime.value = historyTime.value
      lastUpdateTime.value = new Date().toLocaleString('zh-CN')
    } else {
      // 实时模式：使用数据中的时间或当前时间
      if (metricsData && metricsData.metricTime) {
        metricTime.value = formatDateTime(metricsData.metricTime)
        receiveTime.value = formatDateTime(metricsData.receiveTime || metricsData.metricTime)
      } else {
        const now = new Date()
        const timeString = formatDateTime(now)
        metricTime.value = timeString
        receiveTime.value = timeString
      }
      lastUpdateTime.value = new Date().toLocaleString('zh-CN')
    }
  } catch (error) {
    console.error('加载指标数据失败:', error)
    ElMessage.error('加载指标数据失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

// 转换后端指标数据格式为前端格式
const convertBackendMetricsToFrontend = (backendData) => {
  console.log('Converting backend data:', backendData)

  // 计算CPU使用率 - 优先使用cpuPercent字段
  const cpuUsagePercent = backendData.cpuPercent !== undefined ?
    backendData.cpuPercent :
    (backendData.cpuUsage ? (100 - parseFloat(backendData.cpuUsage.idle || 0)) : 0)

  // 计算CPU温度 - 优先使用cpuTemp字段
  const cpuTemp = backendData.cpuTemp !== undefined ?
    backendData.cpuTemp :
    (backendData.temperatures ?
      (backendData.temperatures['soc-thermal'] || backendData.temperatures['gpu-thermal'] || 0) : 0)

  // 内存使用率 - 支持多种字段名格式
  const memoryUsagePercent = backendData.memoryPercent ||
    backendData.memory_usage?.percent ||
    backendData.memoryUsage?.percent || 0

  // 磁盘使用率
  const diskUsagePercent = backendData.diskUsage?.[0]?.percent || 0

  const convertedData = {
    identityMac: backendData.identityMac,
    hostname: backendData.hostname,
    metricTime: backendData.metricTime,
    receiveTime: backendData.receiveTime,
    // 保留原始后端数据，供计算属性使用
    temperatures: backendData.temperatures,
    cpuUsage: backendData.cpuUsage,
    cpuPercent: backendData.cpuPercent,
    cpuTemp: backendData.cpuTemp,
    memoryUsage: backendData.memory_usage || backendData.memoryUsage,
    diskUsage: backendData.diskUsage,
    uptime: backendData.uptime,
    cdata: backendData.cdata,
    zdata: backendData.zdata,
    groupUsage: backendData.groupUsage,
    groupBps: backendData.groupBps,
    // 转换为前端期望的格式
    cpu: {
      usage: cpuUsagePercent,
      temperature: cpuTemp,
      cores: backendData.cpuUsage?.core ? parseInt(backendData.cpuUsage.core) : 4
    },
    memory: {
      usage: memoryUsagePercent,
      total: (backendData.memory_usage?.total || backendData.memoryUsage?.total || 0),
      used: (backendData.memory_usage?.used || backendData.memoryUsage?.used || 0),
      available: (backendData.memory_usage?.available || backendData.memoryUsage?.available || 0),
      free: (backendData.memory_usage?.free || backendData.memoryUsage?.free || 0)
    },
    disk: {
      usage: diskUsagePercent,
      total: backendData.diskUsage?.[0]?.total || 0,
      used: backendData.diskUsage?.[0]?.used || 0,
      available: backendData.diskUsage?.[0]?.free || 0,
      device: backendData.diskUsage?.[0]?.device || '',
      mountpoint: backendData.diskUsage?.[0]?.mountpoint || ''
    },
    network: {
      // 使用磁盘IO速度作为网络速度的替代（如果没有真实网络数据）
      uploadSpeed: backendData.diskUsage?.[0]?.io_rate?.['MBw/s'] ?
                   backendData.diskUsage[0].io_rate['MBw/s'] * 1024 * 1024 : 0, // MB/s转换为B/s
      downloadSpeed: backendData.diskUsage?.[0]?.io_rate?.['MBr/s'] ?
                     backendData.diskUsage[0].io_rate['MBr/s'] * 1024 * 1024 : 0, // MB/s转换为B/s
      totalUpload: 0,   // 后端数据中没有累计上传量
      totalDownload: 0  // 后端数据中没有累计下载量
    }
  }

  console.log('Converted data:', convertedData)
  return convertedData
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return ''

  const date = new Date(dateTime)
  return date.getFullYear() + '-' +
         String(date.getMonth() + 1).padStart(2, '0') + '-' +
         String(date.getDate()).padStart(2, '0') + ' ' +
         String(date.getHours()).padStart(2, '0') + ':' +
         String(date.getMinutes()).padStart(2, '0') + ':' +
         String(date.getSeconds()).padStart(2, '0')
}

// 根据时间范围获取历史数据
const fetchHistoryDataByTimeRange = async (timeRange = currentTimeRange.value) => {
  try {
    console.log(`开始获取${timeRange}历史数据...`, identityMac.value)

    // 调用API获取指定时间范围的历史数据
    const response = await terminalApi.getHistoryMetricsByTimeRange(identityMac.value, timeRange)
    console.log(`${timeRange}历史数据API响应:`, response)

    if (response && response.content && Array.isArray(response.content)) {
      const historyData = []

      console.log(`${timeRange}历史数据详情: 总数据量=${response.totalElements}, 获取到数据量=${response.content.length}`)

      // 处理返回的数据，按时间正序排列（API返回的是倒序）
      const sortedData = response.content.sort((a, b) => {
        const timeA = new Date(a.receiveTime || a.metricTime || a.timestamp)
        const timeB = new Date(b.receiveTime || b.metricTime || b.timestamp)
        return timeA.getTime() - timeB.getTime()
      })

      for (const item of sortedData) {
        // 优先使用receiveTime，如果没有则使用metricTime
        const receiveTime = new Date(item.receiveTime || item.metricTime || item.timestamp)

        const processedData = {
          time: receiveTime,
          receiveTime: item.receiveTime, // 保存原始receiveTime字符串
          metricTime: item.metricTime,   // 保存原始metricTime字符串
          // 保存原始数据用于tooltip显示
          rawData: item,

          // 基础指标 - 根据实际API响应结构调整
          upload: (item.groupBps || 0) / (1024 * 1024), // 转换为 MB/s
          cpuTemp: item.cpuTemp || 0,
          cpuUsage: item.cpuPercent || 0,
          memory: item.memoryPercent || 0,
          disk: (item.diskUsage && item.diskUsage[0]) ? item.diskUsage[0].percent : 0,

          // CPU详细信息
          cpuUserUsage: item.cpuUsage ? parseFloat(item.cpuUsage.user || 0) : 0,
          cpuSystemUsage: item.cpuUsage ? parseFloat(item.cpuUsage.sys || 0) : 0,
          socTemp: item.temperatures ? (item.temperatures['soc-thermal'] || 0) : 0,
          gpuTemp: item.temperatures ? (item.temperatures['gpu-thermal'] || 0) : 0,

          // 内存详细信息
          memoryUsed: item.memoryUsage ? (item.memoryUsage.used / (1024 * 1024 * 1024)) : 0, // 转换为 GB
          memoryAvailable: item.memoryUsage ? (item.memoryUsage.available / (1024 * 1024 * 1024)) : 0, // 转换为 GB

          // 存储健康信息（从实际数据中获取）
          diskHealthTemp: getPreferredDiskTemp(item),
          diskLifeUsed: getPreferredDiskLifeUsed(item),
          diskTotalWritten: getPreferredDiskTotalWritten(item),

          // 内置存储
          internalDiskTemp: getInternalDiskTemp(item),
          internalDiskLifeUsed: getInternalDiskLifeUsed(item),
          internalDiskTotalWritten: getInternalDiskTotalWritten(item),

          // 外置存储
          externalDiskTemp: getExternalDiskTemp(item),
          externalDiskLifeUsed: getExternalDiskLifeUsed(item),
          externalDiskTotalWritten: getExternalDiskTotalWritten(item),

          // 数据文件信息
          collectFileCount: item.cdata ? item.cdata.count : 0,
          compressFileCount: item.zdata ? item.zdata.count : 0,

          // 终端与CPE信息
          totalConnections: item.groupUsage ? Object.keys(item.groupUsage).length : 0,
          totalPps: item.groupBps || 0
        }
        historyData.push(processedData)
      }

      // 保存所有历史数据
      allHistoryData.value = historyData
      metricsHistory.value = historyData

      // 更新显示的数据（最多40个点）
      updateDisplayedHistory()

      console.log(`成功获取${timeRange}历史数据，共 ${historyData.length} 个数据点`)
      console.log('历史数据示例:', historyData.slice(0, 2))

    } else {
      console.warn(`${timeRange}历史数据为空或格式不正确`, response)
      allHistoryData.value = []
      metricsHistory.value = []
      displayedHistory.value = []
    }

  } catch (error) {
    console.error(`获取${timeRange}历史数据失败:`, error)
    ElMessage.warning(`获取${timeRange}历史数据失败: ` + error.message)
    allHistoryData.value = []
    metricsHistory.value = []
    displayedHistory.value = []
  }
}

// 更新显示的历史数据（全量显示）
const updateDisplayedHistory = () => {
  const allData = metricsHistory.value
  // 全量显示：显示所有数据
  displayedHistory.value = [...allData]
  console.log(`全量显示: 显示所有 ${allData.length} 个数据点`)
}



// 生成历史指标数据
const generateHistoryMetrics = async (identityMac, timestamp) => {
  // 模拟延迟
  await new Promise(resolve => setTimeout(resolve, 300))

  // 基于时间戳生成确定的随机数据
  const timeHash = new Date(timestamp).getTime()
  const seed = timeHash % 1000000

  const cpuTemp = 75 + (seed % 20)
  const cpuUsage = 30 + (seed % 40)
  const memoryUsage = 40 + (seed % 30)
  const diskUsage = 50 + (seed % 20)

  return {
    code: 200,
    data: {
      identityMac,
      hostname: 'ec_3568_25030031',
      cpu: {
        usage: cpuUsage,
        temperature: cpuTemp,
        cores: 4
      },
      memory: {
        usage: memoryUsage,
        total: 8 * 1024 * 1024 * 1024, // 8GB
        used: Math.floor(8 * 1024 * 1024 * 1024 * memoryUsage / 100),
        available: Math.floor(8 * 1024 * 1024 * 1024 * (100 - memoryUsage) / 100)
      },
      disk: {
        usage: diskUsage,
        total: 512 * 1024, // 512GB in MB
        used: Math.floor(512 * 1024 * diskUsage / 100),
        available: Math.floor(512 * 1024 * (100 - diskUsage) / 100)
      },
      network: {
        uploadSpeed: Math.floor(seed % 1000) * 1024,
        downloadSpeed: Math.floor(seed % 2000) * 1024,
        totalUpload: Math.floor(seed % 100) * 1024 * 1024,
        totalDownload: Math.floor(seed % 200) * 1024 * 1024
      }
    },
    message: 'success'
  }
}

// 返回上一页
const goBack = () => {
  // 检查是否有来源页面信息
  const from = route.query.from
  if (from === 'alerts') {
    router.push('/alerts')
  } else {
    router.push('/terminals')
  }
}

// 切换到实时指标模式
const goToRealTime = () => {
  router.push({ name: 'RealTimeMetrics', params: { identityMac: identityMac.value } })
}

// 切换到历史指标列表
const goToHistoryList = () => {
  router.push({
    name: 'HistoryMetrics',
    params: { identityMac: identityMac.value }
  })
}

// 测试API连接
const testAPI = async () => {
  try {
    console.log('测试API连接...')
    ElMessage.info('正在测试API连接...')

    // 测试获取最新指标
    const response = await terminalApi.getRealTimeMetrics(identityMac.value)
    console.log('API响应:', response)

    if (response && response.data) {
      ElMessage.success('API连接成功！')
      console.log('获取到的数据:', response.data)
    } else {
      ElMessage.warning('API连接成功，但数据格式异常')
    }
  } catch (error) {
    console.error('API测试失败:', error)
    ElMessage.error('API连接失败: ' + error.message)
  }
}

// 调试函数 - 检查当前数据状态
const debugDataStatus = () => {
  console.log('=== 数据状态调试 ===')
  console.log('identityMac:', identityMac.value)
  console.log('loading:', loading.value)
  console.log('metrics:', metrics.value)
  console.log('metricsHistory length:', metricsHistory.value.length)
  console.log('metricsHistory sample:', metricsHistory.value.slice(0, 2))
  console.log('chart:', chart.value)
  console.log('chartContainer:', chartContainer.value)
  console.log('==================')
}

// 调试悬浮框功能
const debugTooltip = () => {
  console.log('=== 悬浮框调试信息 ===')
  console.log('chart.value:', chart.value)
  console.log('chartContainer.value:', chartContainer.value)
  console.log('displayedHistory.value.length:', displayedHistory.value.length)
  console.log('customTooltip.value:', customTooltip.value)
  console.log('allMetrics enabled:', allMetrics.value.filter(m => m.enabled))
  
  if (chart.value) {
    const zr = chart.value.getZr()
    console.log('ZRender实例:', zr)
    
    // 测试坐标转换
    try {
      const testPixel = chart.value.convertToPixel('grid', [0, 0])
      console.log('坐标转换测试 [0,0] -> pixel:', testPixel)
    } catch (error) {
      console.log('坐标转换测试失败:', error)
    }
  }
  console.log('========================')
}

// 测试悬浮框显示
const testTooltip = () => {
  console.log('测试悬浮框显示...')
  customTooltip.value = {
    visible: true,
    x: 100,
    y: 100,
    time: '12:34:56',
    date: '2024-01-01',
    metrics: [
      {
        key: 'cpuTemp',
        name: 'CPU温度',
        value: '45.2°C',
        color: '#ff4d4f'
      },
      {
        key: 'cpuUsage',
        name: 'CPU使用率',
        value: '65.5%',
        color: '#52c41a'
      }
    ]
  }
  
  // 3秒后隐藏
  setTimeout(() => {
    hideCustomTooltip()
  }, 3000)
}

// 在开发环境下暴露调试函数到全局
if (import.meta.env.DEV) {
  window.debugRealTimeMetrics = debugDataStatus
  window.debugTooltip = debugTooltip
  window.testTooltip = testTooltip
}



// 获取CPU颜色
const getCpuColor = (usage) => {
  if (usage >= 90) return '#F56C6C'
  if (usage >= 70) return '#E6A23C'
  return '#67C23A'
}

// 获取内存颜色
const getMemoryColor = (usage) => {
  if (usage >= 85) return '#F56C6C'
  if (usage >= 70) return '#E6A23C'
  return '#67C23A'
}

// 获取磁盘颜色
const getDiskColor = (usage) => {
  if (usage >= 90) return '#F56C6C'
  if (usage >= 80) return '#E6A23C'
  return '#67C23A'
}

// 获取磁盘样式类
const getDiskClass = (usage) => {
  if (usage >= 90) return 'danger'
  if (usage >= 80) return 'warning'
  return ''
}

// 格式化数值 - 浮点数指标固定2位小数，其他数值四舍五入
const formatMetricValue = (value, isFloatMetric = true) => {
  if (value === null || value === undefined || isNaN(value)) return '0'

  if (isFloatMetric) {
    // 浮点数指标固定为2位小数
    return parseFloat(value).toFixed(2)
  } else {
    // 其他数值四舍五入
    return Math.round(parseFloat(value)).toString()
  }
}

// 格式化温度值
const formatTemperature = (temp) => {
  return formatMetricValue(temp, true) + '°C'
}

// 格式化百分比值
const formatPercentage = (percent) => {
  return formatMetricValue(percent, true) + '%'
}

// 格式化字节数
const formatBytes = (bytes, unit = 'auto') => {
  if (bytes === 0) return '0 B'

  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']

  if (unit === 'MB') {
    return (bytes / (k * k)).toFixed(2) + ' MB'
  } else if (unit === 'KB') {
    return (bytes / k).toFixed(2) + ' KB'
  }

  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 格式化速度
const formatSpeed = (bytesPerSecond) => {
  return formatBytes(bytesPerSecond) + '/s'
}

// 格式化磁盘IO速度
const formatDiskIOSpeed = (mbPerSecond) => {
  if (!mbPerSecond || mbPerSecond === 0) return '0 MB/s'
  return mbPerSecond.toFixed(2) + ' MB/s'
}

// 格式化网络速度 (bps转换为合适的单位)
const formatNetworkSpeed = (bps) => {
  if (!bps || bps === 0) return '0 bps'

  // 8 bit = 1 byte，所以 bps / 8 = Bps (字节每秒)
  const bytesPerSecond = bps

  if (bytesPerSecond >= 1024 * 1024 * 1024) {
    return (bytesPerSecond / (1024 * 1024 * 1024)).toFixed(2) + ' GB/s'
  } else if (bytesPerSecond >= 1024 * 1024) {
    return (bytesPerSecond / (1024 * 1024)).toFixed(2) + ' MB/s'
  } else if (bytesPerSecond >= 1024) {
    return (bytesPerSecond / 1024).toFixed(2) + ' KB/s'
  } else {
    return bytesPerSecond.toFixed(2) + ' B/s'
  }
}

// 格式化包速率
const formatPacketRate = (pps) => {
  if (!pps || pps === 0) return '0 pps'

  if (pps >= 1000000) {
    return (pps / 1000000).toFixed(2) + ' Mpps'
  } else if (pps >= 1000) {
    return (pps / 1000).toFixed(2) + ' Kpps'
  } else {
    return pps.toFixed(0) + ' pps'
  }
}

// 图表相关函数
const initChart = () => {
  if (!chartContainer.value) return

  console.log('初始化图表...')
  chart.value = echarts.init(chartContainer.value)

  // 先更新图表数据
  updateChartData()

  // 等待图表渲染完成后绑定事件
  setTimeout(() => {
    console.log('延迟绑定图表事件...')
    bindChartEvents()
  }, 500) // 增加延迟时间确保图表完全渲染

  // 窗口大小改变时重新调整图表
  window.addEventListener('resize', () => {
    if (chart.value) {
      chart.value.resize()
    }
  })

  // 启动图表自动更新
  if (!isHistoryMode.value) {
    startChartAutoUpdate()
  }
}

const updateChartData = () => {
  if (!chart.value) return

  console.log('更新图表数据，显示数据数量:', displayedHistory.value.length, '总数据数量:', metricsHistory.value.length)

  // 注意：不在这里调用 addToHistory，因为数据应该通过 loadLatestMetrics 添加
  // 这里只负责更新图表显示
  updateDisplayedHistory()

  const timePoints = generateTimePoints()
  const option = createChartOption(timePoints)
  chart.value.setOption(option)

  // 更新变化值
  updateChangeValues()
}



const generateTimePoints = () => {
  // 直接使用displayedHistory中的时间点
  return displayedHistory.value.map(item => item.time)
}

const createChartOption = (timePoints) => {
  const startIndex = Math.max(0, metricsHistory.value.length - timePoints.length)
  const historySlice = metricsHistory.value.slice(startIndex)

  // 获取启用的指标（包括不可见的，用于生成完整的系列数据）
  const enabledMetrics = allMetrics.value.filter(metric => metric.enabled)

  // 获取启用且可见的指标（用于图例显示）
  const visibleMetrics = allMetrics.value.filter(metric => metric.enabled && metric.visible)

  // 动态生成图例数据（只显示可见的指标）
  const legendData = visibleMetrics.map(metric => metric.label.replace(/\s*\([^)]*\)/, ''))

  return {
    tooltip: {
      show: false, // 禁用默认的 tooltip，使用自定义的悬浮弹框
      trigger: 'axis', // 设置触发类型为坐标轴触发，便于获取数据索引
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985'
        }
      }
    },
    legend: {
      data: legendData,
      top: 10,
      textStyle: {
        fontSize: 12
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: currentTimeRange.value === '今日全天' || currentTimeRange.value === '最近24小时' ? '8%' : '3%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: timePoints.map(time => {
        // 统一显示时分秒格式
        return time.toLocaleTimeString('zh-CN', {
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit'
        })
      }),
      axisLabel: {
        rotate: 45,
        fontSize: 10,
        interval: 'auto' // 自动调整标签间隔
      }
    },
    yAxis: [
      {
        type: 'value',
        name: '速率 (MB/s)',
        position: 'left',
        axisLabel: {
          formatter: '{value} MB/s',
          fontSize: 10
        },
        nameTextStyle: {
          fontSize: 10
        }
      },
      {
        type: 'value',
        name: '温度 (°C)',
        position: 'right',
        axisLabel: {
          formatter: '{value}°C',
          fontSize: 10
        },
        nameTextStyle: {
          fontSize: 10
        }
      },
      {
        type: 'value',
        name: '使用率 (%)',
        position: 'right',
        offset: 60,
        axisLabel: {
          formatter: '{value}%',
          fontSize: 10
        },
        nameTextStyle: {
          fontSize: 10
        }
      },
      {
        type: 'value',
        name: '内存 (GB)',
        position: 'left',
        offset: 60,
        axisLabel: {
          formatter: '{value} GB',
          fontSize: 10
        },
        nameTextStyle: {
          fontSize: 10
        }
      },
      {
        type: 'value',
        name: '写入量 (TB)',
        position: 'right',
        offset: 120,
        axisLabel: {
          formatter: '{value} GB',
          fontSize: 10
        },
        nameTextStyle: {
          fontSize: 10
        }
      },
      {
        type: 'value',
        name: '数量',
        position: 'left',
        offset: 120,
        axisLabel: {
          formatter: '{value}',
          fontSize: 10
        },
        nameTextStyle: {
          fontSize: 10
        }
      },
      {
        type: 'value',
        name: '包速率 (pps)',
        position: 'right',
        offset: 180,
        axisLabel: {
          formatter: '{value} pps',
          fontSize: 10
        },
        nameTextStyle: {
          fontSize: 10
        }
      }
    ],
    series: generateSeriesData(historySlice, enabledMetrics),
    dataZoom: [
      {
        type: 'inside',
        start: 0,
        end: 100,
        zoomOnMouseWheel: true,
        moveOnMouseMove: true,
        moveOnMouseWheel: true
      },
      {
        type: 'slider',
        show: getSliderVisibility(),
        start: getSliderStart(),
        end: 100,
        height: 20,
        bottom: 10,
        handleStyle: {
          color: '#1890ff'
        },
        textStyle: {
          color: '#666'
        },
        borderColor: '#ddd',
        fillerColor: 'rgba(24, 144, 255, 0.2)',
        handleIcon: 'M10.7,11.9H9.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4h1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z'
      }
    ]
  }
}

// 获取滑动条显示状态
const getSliderVisibility = () => {
  // 当数据点超过100个时显示滑动条，便于缩放查看
  return metricsHistory.value.length > 100
}

// 获取滑动条起始位置
const getSliderStart = () => {
  // 默认显示所有数据
  return 0
}

// 绑定图表事件
const bindChartEvents = () => {
  if (!chart.value) {
    console.log('图表实例不存在，无法绑定事件')
    return
  }

  console.log('绑定图表事件...')

  // 清除之前的事件监听器
  chart.value.off('mousemove')
  chart.value.off('mouseout')
  chart.value.off('globalout')

  // 使用 ECharts 的 getZr() 方法绑定底层事件
  const zr = chart.value.getZr()
  if (zr) {
    console.log('绑定ZRender事件...')
    
    // 清除之前的ZRender事件
    zr.off('mousemove')
    zr.off('mouseout')
    
    zr.on('mousemove', (event) => {
      if (!chart.value) return
      
      // 通过 ECharts 实例获取当前鼠标位置对应的数据
      const pointInPixel = [event.offsetX, event.offsetY]
      
      // 检查鼠标是否在图表网格区域内
      if (chart.value.containPixel('grid', pointInPixel)) {
        const pointInGrid = chart.value.convertFromPixel('grid', pointInPixel)
        
        if (pointInGrid && pointInGrid[0] !== undefined) {
          const dataIndex = Math.round(pointInGrid[0])
          
          if (dataIndex >= 0 && dataIndex < displayedHistory.value.length) {
            const mockParams = {
              componentType: 'series',
              dataIndex: dataIndex,
              event: event
            }
            showCustomTooltip(mockParams)
          } else {
            hideCustomTooltip()
          }
        } else {
          hideCustomTooltip()
        }
      } else {
        // 鼠标不在图表网格区域时隐藏tooltip
        hideCustomTooltip()
      }
    })

    zr.on('mouseout', () => {
      hideCustomTooltip()
    })
    
    console.log('ZRender事件绑定完成')
  } else {
    console.warn('无法获取ZRender实例')
  }

  // 作为备用方案，也绑定ECharts的原生事件
  chart.value.on('mousemove', (params) => {
    console.log('ECharts mousemove 事件:', params)
    if (params && params.dataIndex !== undefined) {
      showCustomTooltip(params)
    }
  })

  // 鼠标离开图表区域
  chart.value.on('mouseout', () => {
    console.log('鼠标离开图表')
    hideCustomTooltip()
  })

  // 全局鼠标离开事件
  chart.value.on('globalout', () => {
    console.log('全局鼠标离开')
    hideCustomTooltip()
  })
}

// 显示自定义悬浮弹框
const showCustomTooltip = (params) => {
  console.log('显示悬浮弹框:', params)

  const dataIndex = params.dataIndex
  console.log('数据索引:', dataIndex, '历史数据长度:', displayedHistory.value.length)

  if (dataIndex < 0 || dataIndex >= displayedHistory.value.length) {
    console.log('数据索引超出范围')
    return
  }

  const historyItem = displayedHistory.value[dataIndex]
  console.log('历史数据项:', historyItem)

  if (!historyItem) {
    console.log('没有找到历史数据项')
    return
  }

  // 获取鼠标位置
  if (!chartContainer.value) {
    console.log('图表容器不存在')
    return
  }

  const chartRect = chartContainer.value.getBoundingClientRect()
  let x, y

  // 优先使用 ECharts 事件中的位置信息
  if (params.event && params.event.offsetX !== undefined) {
    x = params.event.offsetX + 15
    y = params.event.offsetY - 10
  } else if (chart.value) {
    // 备用方案：使用图表坐标转换
    try {
      const pixel = chart.value.convertToPixel('grid', [dataIndex, 0])
      if (pixel && Array.isArray(pixel) && pixel.length >= 2) {
        x = pixel[0] + 15
        y = pixel[1] - 10
      } else {
        // 如果坐标转换失败，使用默认位置
        x = 100
        y = 100
      }
    } catch (error) {
      console.warn('坐标转换失败:', error)
      x = 100
      y = 100
    }
  } else {
    x = 100
    y = 100
  }

  console.log('鼠标位置:', { x, y, chartRect })

  // 格式化时间
  const time = new Date(historyItem.time)
  const timeStr = time.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
  const dateStr = time.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  })

  // 获取当前激活且可见的指标
  const visibleMetrics = allMetrics.value.filter(metric => metric.enabled && metric.visible)
  console.log('可见的指标:', visibleMetrics)

  const tooltipMetrics = []

  visibleMetrics.forEach(metric => {
    const value = historyItem[metric.key]
    if (value !== undefined && value !== null) {
      tooltipMetrics.push({
        key: metric.key,
        name: metric.label.replace(/\s*\([^)]*\)/, ''),
        value: formatTooltipMetricValue(value, metric.key),
        color: metric.color
      })
    }
  })

  console.log('悬浮弹框指标:', tooltipMetrics)

  // 边界检查和位置调整
  const tooltipWidth = 250
  const tooltipHeight = 100 + (tooltipMetrics.length * 25) // 估算高度

  // 防止超出右边界
  if (x + tooltipWidth > chartRect.width) {
    x = chartRect.width - tooltipWidth - 10
  }

  // 防止超出下边界
  if (y + tooltipHeight > chartRect.height) {
    y = chartRect.height - tooltipHeight - 10
  }

  // 防止超出上边界
  if (y < 10) {
    y = 10
  }

  // 防止超出左边界
  if (x < 10) {
    x = 10
  }

  // 更新悬浮弹框数据
  customTooltip.value = {
    visible: true,
    x: x,
    y: y,
    time: timeStr,
    date: dateStr,
    metrics: tooltipMetrics
  }

  console.log('悬浮弹框数据已更新:', customTooltip.value)
}

// 隐藏自定义悬浮弹框
const hideCustomTooltip = () => {
  customTooltip.value.visible = false
}

// 获取当前激活的指标
const getActiveMetrics = () => {
  return allMetrics.value.filter(metric => metric.enabled && metric.visible)
}

// 格式化悬浮弹框中的指标值
const formatTooltipMetricValue = (value, metricKey) => {
  const numValue = parseFloat(value)

  if (isNaN(numValue)) return value

  // 根据指标类型格式化
  switch (metricKey) {
    case 'cpuTemp':
    case 'socTemp':
    case 'gpuTemp':
    case 'diskHealthTemp':
    case 'internalDiskTemp':
    case 'externalDiskTemp':
      return `${numValue.toFixed(1)}°C`
    case 'cpuUsage':
    case 'memory':
    case 'disk':
    case 'cpuUserUsage':
    case 'cpuSystemUsage':
    case 'diskLifeUsed':
    case 'internalDiskLifeUsed':
    case 'externalDiskLifeUsed':
      return `${numValue.toFixed(1)}%`
    case 'upload':
      return `${numValue.toFixed(2)} MB/s`
    case 'memoryUsed':
    case 'memoryAvailable':
      return `${numValue.toFixed(2)} GB`
    case 'diskTotalWritten':
    case 'internalDiskTotalWritten':
    case 'externalDiskTotalWritten':
      return `${numValue.toFixed(2)} TB`
    case 'collectFileCount':
    case 'compressFileCount':
    case 'totalConnections':
      return numValue.toString()
    case 'totalPps':
      return formatPacketRate(numValue)
    default:
      return numValue.toFixed(2)
  }
}

// 动态生成图表系列数据
const generateSeriesData = (historySlice, enabledMetrics) => {
  // 使用displayedHistory.value而不是historySlice参数
  const dataSource = displayedHistory.value

  return enabledMetrics.map(metric => {
    let data = []
    let precision = 1

    // 根据指标类型获取数据
    switch (metric.key) {
      // 基础指标
      case 'upload':
        data = dataSource.map(item => item.upload.toFixed(2))
        precision = 2
        break
      case 'cpuTemp':
        data = dataSource.map(item => item.cpuTemp.toFixed(1))
        break
      case 'cpuUsage':
        data = dataSource.map(item => item.cpuUsage.toFixed(1))
        break
      case 'memory':
        data = dataSource.map(item => item.memory.toFixed(1))
        break
      case 'disk':
        data = dataSource.map(item => item.disk.toFixed(1))
        break

      // CPU详细信息
      case 'cpuUserUsage':
        data = dataSource.map(item => item.cpuUserUsage.toFixed(1))
        break
      case 'cpuSystemUsage':
        data = dataSource.map(item => item.cpuSystemUsage.toFixed(1))
        break
      case 'socTemp':
        data = dataSource.map(item => item.socTemp.toFixed(1))
        break
      case 'gpuTemp':
        data = dataSource.map(item => item.gpuTemp.toFixed(1))
        break

      // 内存详细信息
      case 'memoryUsed':
        data = dataSource.map(item => item.memoryUsed.toFixed(2))
        precision = 2
        break
      case 'memoryAvailable':
        data = dataSource.map(item => item.memoryAvailable.toFixed(2))
        precision = 2
        break

      // 通用存储健康信息（优先使用外置存储）
      case 'diskHealthTemp':
        data = dataSource.map(item => item.diskHealthTemp.toFixed(1))
        break
      case 'diskLifeUsed':
        data = dataSource.map(item => item.diskLifeUsed.toFixed(1))
        break
      case 'diskTotalWritten':
        data = dataSource.map(item => item.diskTotalWritten.toFixed(2))
        precision = 2
        break

      // 内置存储健康信息
      case 'internalDiskTemp':
        data = dataSource.map(item => item.internalDiskTemp.toFixed(1))
        break
      case 'internalDiskLifeUsed':
        data = dataSource.map(item => item.internalDiskLifeUsed.toFixed(1))
        break
      case 'internalDiskTotalWritten':
        data = dataSource.map(item => item.internalDiskTotalWritten.toFixed(2))
        precision = 2
        break

      // 外置存储健康信息
      case 'externalDiskTemp':
        data = dataSource.map(item => item.externalDiskTemp.toFixed(1))
        break
      case 'externalDiskLifeUsed':
        data = dataSource.map(item => item.externalDiskLifeUsed.toFixed(1))
        break
      case 'externalDiskTotalWritten':
        data = dataSource.map(item => item.externalDiskTotalWritten.toFixed(2))
        precision = 2
        break

      // 数据文件信息
      case 'collectFileCount':
        data = dataSource.map(item => item.collectFileCount)
        precision = 0
        break
      case 'compressFileCount':
        data = dataSource.map(item => item.compressFileCount)
        precision = 0
        break

      // 终端与CPE信息
      case 'totalConnections':
        data = dataSource.map(item => item.totalConnections)
        precision = 0
        break
      case 'totalPps':
        data = dataSource.map(item => item.totalPps.toFixed(0))
        precision = 0
        break

      default:
        data = dataSource.map(() => 0)
    }

    const series = {
      name: metric.label.replace(/\s*\([^)]*\)/, ''),
      type: 'line',
      yAxisIndex: metric.yAxisIndex,
      data: data,
      smooth: true,
      symbol: 'circle',
      symbolSize: 4,
      lineStyle: {
        color: metric.color,
        width: 2,
        opacity: metric.visible ? 1 : 0 // 根据可见性设置透明度
      },
      itemStyle: {
        color: metric.color,
        opacity: metric.visible ? 1 : 0 // 根据可见性设置透明度
      },
      // 根据可见性控制系列显示/隐藏
      show: metric.visible
    }

    // 只有上行速率显示面积图
    if (metric.key === 'upload') {
      series.areaStyle = {
        color: {
          type: 'linear',
          x: 0, y: 0, x2: 0, y2: 1,
          colorStops: [
            { offset: 0, color: `rgba(24, 144, 255, ${metric.visible ? 0.3 : 0})` },
            { offset: 1, color: `rgba(24, 144, 255, ${metric.visible ? 0.1 : 0})` }
          ]
        }
      }
    }

    return series
  })
}

// 加载用户指标配置
const loadMetricsConfig = () => {
  try {
    const savedConfig = localStorage.getItem(`metrics-config-${identityMac.value}`)
    if (savedConfig) {
      const config = JSON.parse(savedConfig)
      config.forEach(savedMetric => {
        const metric = allMetrics.value.find(m => m.key === savedMetric.key)
        if (metric) {
          metric.enabled = savedMetric.enabled
          metric.visible = savedMetric.visible
        }
      })
    } else {
      // 如果没有保存的配置，设置默认配置
      const defaultEnabledKeys = ['upload', 'cpuTemp', 'cpuUsage']
      allMetrics.value.forEach(metric => {
        metric.enabled = defaultEnabledKeys.includes(metric.key)
        metric.visible = true
      })
    }
  } catch (error) {
    console.warn('加载指标配置失败:', error)
    // 出错时也设置默认配置
    const defaultEnabledKeys = ['upload', 'cpuTemp', 'cpuUsage']
    allMetrics.value.forEach(metric => {
      metric.enabled = defaultEnabledKeys.includes(metric.key)
      metric.visible = true
    })
  }
}

// 添加缺失的函数定义
const getCpuUserUsage = (data) => {
  if (data && data.cpuUsage && data.cpuUsage.user) {
    return parseFloat(data.cpuUsage.user)
  }
  return 0
}

const getCpuSystemUsage = (data) => {
  if (data && data.cpuUsage && data.cpuUsage.sys) {
    return parseFloat(data.cpuUsage.sys)
  }
  return 0
}

const getSocTemperature = (data) => {
  if (data?.temperatures?.soc !== undefined) {
    return data.temperatures.soc
  }
  return data?.socTemp || 0
}

const getGpuTemperature = (data) => {
  if (data?.temperatures?.gpu !== undefined) {
    return data.temperatures.gpu
  }
  return data?.gpuTemp || 0
}

const getMemoryUsed = (data) => {
  if (data && data.memoryUsage && data.memoryUsage.used) {
    return data.memoryUsage.used
  }
  return data?.memory?.used || 0
}

const getMemoryAvailable = (data) => {
  if (data && data.memoryUsage && data.memoryUsage.available) {
    return data.memoryUsage.available
  }
  return data?.memory?.available || data?.memory?.free || 0
}

const getCdataCount = (data) => {
  if (data && data.cdata && typeof data.cdata.count === 'number') {
    return data.cdata.count
  }
  return 0
}

const getZdataCount = (data) => {
  if (data && data.zdata && typeof data.zdata.count === 'number') {
    return data.zdata.count
  }
  return 0
}

const getTotalConnections = (data) => {
  if (data?.groupUsage?.buckets) {
    return data.groupUsage.buckets.reduce((total, bucket) => total + (bucket.conn || 0), 0)
  }
  return 0
}

const getTotalPps = (data) => {
  return data?.groupUsage?.pps_total || data?.group_usage?.pps_total || 0
}

const updateChangeValues = () => {
  if (metricsHistory.value.length < 2) return

  const current = metricsHistory.value[metricsHistory.value.length - 1]
  const previous = metricsHistory.value[metricsHistory.value.length - 2]

  uploadChange.value = current.upload - previous.upload
  cpuTempChange.value = current.cpuTemp - previous.cpuTemp
  cpuUsageChange.value = current.cpuUsage - previous.cpuUsage
  memoryChange.value = current.memory - previous.memory
  diskChange.value = current.disk - previous.disk
}

const getMetricUnit = (seriesName) => {
  switch (seriesName) {
    case '上行速率': return ' MB/s'
    case 'CPU温度': return '°C'
    case 'CPU使用率':
    case '内存使用率':
    case '磁盘使用率': return '%'
    default: return ''
  }
}

const switchTimeRange = async (range) => {
  console.log(`切换时间范围: ${currentTimeRange.value} → ${range}`)
  currentTimeRange.value = range

  try {
    // 根据新的时间范围重新获取数据
    await fetchHistoryDataByTimeRange(range)

    // 更新图表
    updateChartData()

    ElMessage.success(`已切换到${range}`)
  } catch (error) {
    console.error('切换时间范围失败:', error)
    ElMessage.error(`切换到${range}失败: ` + error.message)
  }
}

const refreshChartData = () => {
  updateChartData()
}

const startChartAutoUpdate = () => {
  if (isHistoryMode.value) return

  // 图表更新现在由数据更新驱动，不需要定时更新
  // 数据更新在 loadLatestMetrics 中处理，只有在有新数据时才会更新图表
  console.log('图表自动更新已启用，将由数据更新驱动')
}

const getChangeClass = (change) => {
  if (Math.abs(change) < 0.1) return 'no-change'
  return change > 0 ? 'increase' : 'decrease'
}

const formatChange = (change) => {
  if (Math.abs(change) < 0.1) return '0'
  const sign = change > 0 ? '+' : ''
  return sign + change.toFixed(1)
}
</script>

<style scoped>
.real-time-metrics-page {
  /* 继承通用样式 */
}

/* 面包屑样式 */
.breadcrumb {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  font-size: 14px;
}

.breadcrumb a {
  color: #667eea;
  text-decoration: none;
  cursor: pointer;
}

.breadcrumb a:hover {
  text-decoration: underline;
}

.breadcrumb span {
  margin: 0 8px;
  color: #999;
}

/* 刷新信息 */
.refresh-info {
  background: #e3f2fd;
  border: 1px solid #bbdefb;
  border-radius: 4px;
  padding: 10px;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.refresh-info i {
  color: #1976d2;
}

/* 时间信息 */
.time-info {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 12px;
  margin-bottom: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 13px;
}

.time-label {
  color: #6c757d;
  font-weight: 500;
}

.time-value {
  color: #495057;
  font-weight: 600;
}

.history-badge {
  background-color: #e0e0e0;
  color: #333;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
}

/* 指标网格 */
.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 24px;
  margin-top: 8px;
}

/* 错误状态样式 */
.empty-message {
  text-align: center;
  padding: 40px 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.error-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.error-title {
  font-size: 18px;
  font-weight: 600;
  color: #dc3545;
  margin-bottom: 16px;
}

.error-details {
  text-align: left;
  max-width: 400px;
  margin: 0 auto 24px;
  padding: 16px;
  background: #fff;
  border-radius: 6px;
  border: 1px solid #dee2e6;
}

.error-details p {
  margin: 0 0 8px 0;
  font-weight: 600;
  color: #495057;
}

.error-details ul {
  margin: 0;
  padding-left: 20px;
  color: #6c757d;
}

.error-details li {
  margin-bottom: 4px;
}

.error-actions {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
}

.error-actions .btn {
  display: inline-flex;
  align-items: center;
  gap: 6px;
}

/* 旧的metric-section样式已被新样式覆盖 */

.metric-section .metric-row {
  margin-bottom: 12px;
  padding: 8px 0;
  border-bottom: 1px solid #dee2e6;
}

.metric-section .metric-row:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.metric-section .metric-label {
  font-size: 13px;
  color: #6c757d;
  margin-bottom: 5px;
  font-weight: 500;
}

.metric-section .metric-value {
  font-size: 14px;
  color: #212529;
  font-weight: 600;
}

/* 进度条样式 */
.progress-bar {
  width: 100%;
  height: 8px;
  background: #eee;
  border-radius: 4px;
  overflow: hidden;
  margin-top: 5px;
}

.progress-fill {
  height: 100%;
  background: #667eea;
  transition: width 0.3s ease;
}

.progress-fill.warning {
  background: #ffc107;
}

.progress-fill.danger {
  background: #dc3545;
}

/* 加载状态 */
.loading-container {
  text-align: center;
  padding: 40px;
}

.loading-text {
  color: #666;
  font-size: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .time-info {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }

  .metrics-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .metric-section {
    padding: 12px;
  }

  .metric-section h5 {
    font-size: 13px;
  }

  .network-card {
    padding: 15px;
  }

  .network-value {
    font-size: 24px;
  }

  .bucket-stats {
    flex-direction: column;
    gap: 8px;
  }

  .bucket-stat {
    text-align: center;
  }

  .overview-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 10px;
  }

  .overview-item {
    padding: 12px;
  }

  .overview-value {
    font-size: 16px;
  }

  .cpe-groups-grid {
    grid-template-columns: 1fr;
  }

  .cpe-group-stats {
    gap: 6px;
  }

  .metric-section {
    padding: 15px;
  }

  .metric-section:hover {
    transform: none;
  }

  .memory-summary {
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 8px;
  }

  .memory-item {
    padding: 10px;
  }

  .memory-value {
    font-size: 12px;
  }

  .memory-detail-grid {
    grid-template-columns: 1fr;
    gap: 6px;
  }

  .memory-detail-item {
    padding: 6px 10px;
  }
}

@media (max-width: 480px) {
  .breadcrumb {
    margin-bottom: 15px;
    font-size: 12px;
  }
  
  .refresh-info {
    flex-direction: column;
    text-align: center;
    gap: 5px;
  }
  
  .time-info {
    font-size: 12px;
  }
  
  .metrics-grid {
    gap: 10px;
  }
  
  .card-body {
    padding: 15px;
  }
  
  .stat-number {
    font-size: 24px;
  }
}

.card-header {
  padding: 20px;
  border-bottom: 1px solid #eee;
  background: #fafafa;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 10px;
  align-items: center;
}

.header-actions .btn {
  display: flex;
  align-items: center;
  gap: 5px;
}

/* 网络流量信息样式 */
.network-info {
  margin-bottom: 20px;
}

.network-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px;
  border-radius: 12px;
  text-align: center;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.network-title {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 8px;
  opacity: 0.9;
}

.network-value {
  font-size: 28px;
  font-weight: 700;
  margin-bottom: 5px;
}

.network-subtitle {
  font-size: 12px;
  opacity: 0.8;
}

/* 存储设备样式 */
.storage-device {
  margin-bottom: 15px;
  padding: 12px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
}

.storage-device:last-child {
  margin-bottom: 0;
}

.storage-header {
  font-weight: 600;
  color: #495057;
  margin-bottom: 10px;
  padding-bottom: 5px;
  border-bottom: 1px solid #dee2e6;
  font-size: 13px;
}

/* 详细指标信息优化样式 */
.metric-section {
  background: #ffffff;
  border: 1px solid #e8eaed;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
}

.metric-section:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  transform: translateY(-2px);
}

.metric-section h5 {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #1a73e8;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid #f1f3f4;
}

/* 图标样式 */
.metric-section h5 i::before {
  content: "●";
  font-style: normal;
  font-size: 12px;
}

.system-section h5 i::before { color: #34a853; }
.cpu-section h5 i::before { color: #ea4335; }
.memory-section h5 i::before { color: #fbbc04; }
.storage-section h5 i::before { color: #4285f4; }
.terminal-cpe-section h5 i::before { color: #9c27b0; }
.data-section h5 i::before { color: #ff9800; }

/* 终端与CPE信息样式 */
.terminal-cpe-section {
  grid-column: 1 / -1; /* 占满整行 */
}

.terminal-overview {
  margin-bottom: 20px;
}

.overview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
}

.overview-item {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 15px;
  border-radius: 8px;
  text-align: center;
}

.overview-label {
  font-size: 12px;
  opacity: 0.9;
  margin-bottom: 5px;
}

.overview-value {
  font-size: 18px;
  font-weight: 600;
}

/* CPE状态监控样式 */
.cpe-status-alert {
  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
  border: 1px solid #ffc107;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(255, 193, 7, 0.2);
}

.status-alert-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 12px;
}

.status-alert-icon {
  font-size: 20px;
}

.status-alert-title {
  font-size: 16px;
  font-weight: bold;
  color: #856404;
}

.status-alert-count {
  background: #ffc107;
  color: #212529;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
}

.offline-cpe-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.offline-cpe-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(255, 255, 255, 0.7);
  padding: 12px;
  border-radius: 6px;
  border-left: 3px solid #dc3545;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.offline-cpe-info {
  flex: 1;
}

.offline-cpe-name {
  font-weight: bold;
  margin-bottom: 4px;
  color: #dc3545;
}

.offline-cpe-details {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  font-size: 12px;
  color: #6c757d;
}

.offline-detail {
  background: rgba(108, 117, 125, 0.1);
  padding: 2px 6px;
  border-radius: 4px;
}

.offline-cpe-status {
  margin-left: 12px;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: bold;
  text-transform: uppercase;
}

.status-badge.offline {
  background: #dc3545;
  color: white;
}

.cpe-groups-header {
  font-size: 14px;
  font-weight: 600;
  color: #5f6368;
  margin-bottom: 12px;
  padding-left: 8px;
  border-left: 3px solid #1a73e8;
}

.cpe-groups-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
}

.cpe-group-card {
  background: #f8f9fa;
  border: 1px solid #e8eaed;
  border-radius: 8px;
  padding: 15px;
  transition: all 0.2s ease;
}

.cpe-group-card:hover {
  background: #f1f3f4;
  border-color: #1a73e8;
}

.cpe-group-title {
  font-weight: 600;
  color: #1a73e8;
  margin-bottom: 12px;
  font-size: 14px;
}

.cpe-group-stats {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.cpe-stat {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 0;
  border-bottom: 1px solid #e8eaed;
}

.cpe-stat:last-child {
  border-bottom: none;
}

.cpe-stat-label {
  font-size: 12px;
  color: #5f6368;
  font-weight: 500;
}

.cpe-stat-value {
  font-size: 13px;
  color: #202124;
  font-weight: 600;
}

/* 内存信息样式 */
.memory-overview {
  margin-bottom: 20px;
}

.memory-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 12px;
  margin-bottom: 12px;
}

.memory-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px;
  background: linear-gradient(135deg, #fbbc04 0%, #f9ab00 100%);
  color: white;
  border-radius: 8px;
  text-align: center;
}

.memory-label {
  font-size: 11px;
  opacity: 0.9;
  margin-bottom: 4px;
}

.memory-value {
  font-size: 14px;
  font-weight: 600;
}

.memory-details {
  margin-top: 16px;
}

.memory-detail-title {
  font-size: 13px;
  font-weight: 600;
  color: #5f6368;
  margin-bottom: 12px;
  padding-left: 8px;
  border-left: 3px solid #fbbc04;
}

.memory-detail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: 8px;
}

.memory-detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 3px solid #fbbc04;
}

.detail-label {
  font-size: 12px;
  color: #5f6368;
  font-weight: 500;
}

.detail-value {
  font-size: 12px;
  color: #202124;
  font-weight: 600;
}

/* 图表容器样式 */
.trends-container {
  background: #ffffff;
  border: 1px solid #e8eaed;
  border-radius: 12px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.trends-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #f1f3f4;
  background: #fafbfc;
  border-radius: 12px 12px 0 0;
}

.trends-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #1a73e8;
}

.trends-controls {
  display: flex;
  align-items: center;
  gap: 15px;
  flex-wrap: wrap;
}

.time-range-controls,
.other-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.time-range-selector {
  padding: 6px 12px;
  border: 1px solid #dadce0;
  border-radius: 6px;
  background: #ffffff;
  color: #5f6368;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.time-range-selector:hover {
  border-color: #1a73e8;
  color: #1a73e8;
}

.time-range-selector.active {
  background: #1a73e8;
  border-color: #1a73e8;
  color: #ffffff;
}

.refresh-btn {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 6px 12px;
  border: 1px solid #34a853;
  border-radius: 6px;
  background: #34a853;
  color: #ffffff;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.refresh-btn:hover {
  background: #2d8f47;
  border-color: #2d8f47;
}

.metrics-config-btn {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 6px 12px;
  border: 1px solid #1a73e8;
  border-radius: 6px;
  background: #1a73e8;
  color: #ffffff;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.metrics-config-btn:hover {
  background: #1557b0;
  border-color: #1557b0;
}

.trends-body {
  padding: 20px;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  flex-wrap: wrap;
  gap: 15px;
}

.chart-title {
  font-size: 14px;
  font-weight: 600;
  color: #202124;
  display: flex;
  align-items: center;
  gap: 8px;
}

.data-points-info {
  font-size: 12px;
  font-weight: normal;
  color: #5f6368;
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  padding: 4px 8px;
  border-radius: 12px;
  border: 1px solid #2196f3;
}

.live-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #34a853;
}

.live-dot {
  width: 8px;
  height: 8px;
  background: #34a853;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

.chart-legend {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  align-items: center;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #5f6368;
}

.legend-color {
  width: 12px;
  height: 3px;
  border-radius: 2px;
}

.legend-upload { background: #1890ff; }
.legend-cpu-temp { background: #ff4d4f; }
.legend-cpu-usage { background: #52c41a; }
.legend-memory { background: #faad14; }
.legend-disk { background: #722ed1; }

/* 图例交互样式 */
.legend-item.clickable {
  cursor: pointer;
  transition: all 0.2s ease;
  padding: 4px 8px;
  border-radius: 4px;
  position: relative;
}

.legend-item.clickable:hover {
  background: rgba(26, 115, 232, 0.1);
}

.legend-item.disabled {
  opacity: 0.5;
}

.visibility-icon {
  margin-left: 6px;
  font-size: 12px;
}

/* 指标配置弹框样式 */
.metrics-config-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.metrics-config-content {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.config-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e9ecef;
  background: #f8f9fa;
}

.config-header h3 {
  margin: 0;
  color: #212529;
  font-size: 18px;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  color: #6c757d;
  cursor: pointer;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: #e9ecef;
  color: #495057;
}

.config-body {
  padding: 24px;
  max-height: 50vh;
  overflow-y: auto;
}

.config-section {
  margin-bottom: 24px;
}

.config-section h4 {
  margin: 0 0 16px 0;
  color: #495057;
  font-size: 16px;
  font-weight: 600;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.metrics-limit-info {
  font-size: 12px;
  color: #6c757d;
  background: #f8f9fa;
  padding: 4px 12px;
  border-radius: 12px;
  border: 1px solid #e9ecef;
  transition: all 0.2s ease;
}

.metrics-limit-info.limit-warning {
  color: #e6a23c;
  background: #fef9e7;
  border-color: #fadb14;
}

.metrics-categories {
  display: grid;
  gap: 20px;
}

.category-section {
  border: 1px solid #e9ecef;
  border-radius: 8px;
  overflow: hidden;
}

.category-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.category-header h5 {
  margin: 0;
  color: #495057;
  font-size: 14px;
  font-weight: 600;
}

.category-count {
  font-size: 12px;
  color: #6c757d;
  background: #e9ecef;
  padding: 2px 8px;
  border-radius: 12px;
}

.metrics-list {
  display: grid;
  gap: 8px;
  padding: 16px;
}

.metric-item {
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 16px;
  transition: all 0.2s ease;
}

.metric-item:hover {
  border-color: #1a73e8;
  box-shadow: 0 2px 8px rgba(26, 115, 232, 0.1);
}

.metric-checkbox {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  cursor: pointer;
  margin: 0;
}

.metric-checkbox input[type="checkbox"] {
  display: none;
}

.checkmark {
  width: 20px;
  height: 20px;
  border: 2px solid #dee2e6;
  border-radius: 4px;
  position: relative;
  transition: all 0.2s ease;
  flex-shrink: 0;
  margin-top: 2px;
}

.metric-checkbox input[type="checkbox"]:checked + .checkmark {
  background: #1a73e8;
  border-color: #1a73e8;
}

.metric-checkbox input[type="checkbox"]:checked + .checkmark::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  font-weight: bold;
}

.metric-info {
  flex: 1;
}

.metric-name {
  font-weight: 600;
  color: #212529;
  margin-bottom: 4px;
}

.metric-desc {
  font-size: 13px;
  color: #6c757d;
  line-height: 1.4;
}

.selected-metrics {
  display: grid;
  gap: 8px;
}

.selected-metric-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.selected-metric-item .metric-name {
  flex: 1;
  margin: 0;
  font-size: 14px;
}

.visibility-toggle {
  padding: 4px 12px;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  background: white;
  color: #6c757d;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.visibility-toggle.visible {
  background: #28a745;
  border-color: #28a745;
  color: white;
}

.visibility-toggle:hover {
  border-color: #1a73e8;
  color: #1a73e8;
}

.visibility-toggle.visible:hover {
  background: #218838;
  border-color: #218838;
}

.config-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px 24px;
  border-top: 1px solid #e9ecef;
  background: #f8f9fa;
}

.btn-secondary {
  padding: 8px 16px;
  border: 1px solid #6c757d;
  border-radius: 6px;
  background: white;
  color: #6c757d;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-secondary:hover {
  background: #6c757d;
  color: white;
}

.btn-primary {
  padding: 8px 16px;
  border: 1px solid #1a73e8;
  border-radius: 6px;
  background: #1a73e8;
  color: white;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-primary:hover {
  background: #1557b0;
  border-color: #1557b0;
}

.chart-wrapper {
  position: relative;
  width: 100%;
  height: 400px;
}

.chart-canvas {
  width: 100%;
  height: 100%;
  position: relative;
}

/* 自定义悬浮弹框样式 */
.custom-tooltip {
  position: absolute;
  background: rgba(0, 0, 0, 0.85);
  backdrop-filter: blur(10px);
  border-radius: 8px;
  padding: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
  z-index: 1000;
  min-width: 200px;
  max-width: 300px;
  pointer-events: none;
  transform: translateY(-50%);
}

.tooltip-header {
  margin-bottom: 8px;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.tooltip-time {
  color: #ffffff;
  font-size: 14px;
  font-weight: bold;
  margin-bottom: 2px;
}

.tooltip-date {
  color: #cccccc;
  font-size: 12px;
}

.tooltip-content {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.tooltip-metric {
  display: flex;
  align-items: center;
  gap: 8px;
}

.metric-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  flex-shrink: 0;
}

.metric-info {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.metric-name {
  color: #ffffff;
  font-size: 12px;
}

.metric-value {
  font-size: 12px;
  font-weight: bold;
}

.current-values {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-top: 20px;
}

.value-card {
  background: #ffffff;
  border: 1px solid #e8eaed;
  border-radius: 8px;
  padding: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.2s ease;
}

.value-card:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-color: #1a73e8;
}

.value-info {
  flex: 1;
}

.value-title {
  font-size: 12px;
  color: #5f6368;
  margin-bottom: 4px;
}

.value-number {
  font-size: 18px;
  font-weight: 600;
  color: #202124;
}

.value-upload-num { color: #1890ff; }
.value-cpu-temp-num { color: #ff4d4f; }
.value-cpu-usage-num { color: #52c41a; }
.value-memory-num { color: #faad14; }
.value-disk-num { color: #722ed1; }

.value-change {
  font-size: 12px;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 4px;
  min-width: 50px;
  text-align: center;
}

.value-change.increase {
  background: #f6ffed;
  color: #52c41a;
}

.value-change.decrease {
  background: #fff2f0;
  color: #ff4d4f;
}

.value-change.no-change {
  background: #f5f5f5;
  color: #8c8c8c;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .trends-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }

  .trends-controls {
    justify-content: center;
  }

  .chart-header {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
  }

  .chart-legend {
    justify-content: center;
  }

  .current-values {
    grid-template-columns: 1fr;
    gap: 10px;
  }

  .custom-tooltip {
    min-width: 180px;
    max-width: 250px;
  }

  .metrics-config-content {
    width: 95%;
    max-height: 90vh;
  }

  .config-body {
    max-height: 60vh;
  }

  .metrics-list {
    gap: 6px;
  }

  .metric-item {
    padding: 12px;
  }

  .current-values {
    grid-template-columns: 1fr;
  }

  .value-card {
    padding: 12px;
  }

  .value-number {
    font-size: 16px;
  }
}

/* 存储设备样式增强 */
.storage-section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.storage-toggle-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border: 1px solid #dadce0;
  border-radius: 6px;
  background: #ffffff;
  color: #5f6368;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.storage-toggle-btn:hover {
  border-color: #1a73e8;
  color: #1a73e8;
}

.toggle-icon {
  font-size: 10px;
  transition: transform 0.2s ease;
}

.toggle-icon.expanded {
  transform: rotate(180deg);
}

.storage-details {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e8eaed;
}

.primary-storage {
  border-left: 4px solid #1a73e8;
}

.secondary-storage {
  border-left: 4px solid #34a853;
}

.empty-storage {
  border-left: 4px solid #9aa0a6;
  opacity: 0.7;
}

.storage-badge {
  font-size: 10px;
  font-weight: bold;
  padding: 2px 6px;
  border-radius: 8px;
  text-transform: uppercase;
}

.storage-badge.primary {
  background: #1a73e8;
  color: white;
}

.storage-badge.secondary {
  background: #34a853;
  color: white;
}

.storage-badge.empty {
  background: #9aa0a6;
  color: white;
}

/* 硬盘健康样式 */
.disk-health-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 8px;
  margin-top: 8px;
}

.health-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.health-label {
  font-size: 11px;
  color: #6c757d;
  margin-bottom: 4px;
  text-align: center;
}

.health-value {
  font-size: 12px;
  font-weight: 600;
  text-align: center;
}

  .trends-controls {
    justify-content: center;
    flex-wrap: wrap;
    gap: 10px;
  }

  .time-range-controls,
  .other-controls {
    gap: 6px;
  }

  .time-range-selector {
    padding: 6px 10px;
    font-size: 11px;
  }

  .chart-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .chart-legend {
    justify-content: center;
  }

  .chart-wrapper {
    height: 300px;
  }

  .chart-canvas {
    height: 100%;
  }

  .current-values {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 10px;
  }

  .value-card {
    padding: 12px;
  }

  .value-number {
    font-size: 16px;
  }

/* 存储信息下拉样式 */
.storage-section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.storage-toggle-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  color: #495057;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.storage-toggle-btn:hover {
  background: #e9ecef;
  border-color: #adb5bd;
}

.toggle-icon {
  font-size: 10px;
  transition: transform 0.2s ease;
}

.toggle-icon.expanded {
  transform: rotate(180deg);
}

.storage-details {
  margin-top: 16px;
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.storage-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.storage-title {
  font-size: 14px;
  font-weight: 600;
  color: #495057;
}

.storage-badge {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.storage-badge.primary {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.storage-badge.secondary {
  background: #d1ecf1;
  color: #0c5460;
  border: 1px solid #bee5eb;
}

.storage-badge.empty {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.primary-storage {
  border-left: 4px solid #28a745;
}

.secondary-storage {
  border-left: 4px solid #17a2b8;
}

.empty-storage {
  border-left: 4px solid #dc3545;
  opacity: 0.7;
}

/* 硬盘健康情况样式 */
.disk-health-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 12px;
  margin-top: 8px;
}

.health-item {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 10px 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  transition: all 0.2s ease;
}

.health-item:hover {
  background: #ffffff;
  border-color: #007bff;
  box-shadow: 0 2px 4px rgba(0, 123, 255, 0.1);
}

.health-label {
  font-size: 11px;
  color: #6c757d;
  font-weight: 500;
  margin-bottom: 4px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.health-value {
  font-size: 14px;
  font-weight: 600;
  color: #212529;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .storage-section-header {
    flex-direction: column;
    gap: 10px;
    align-items: stretch;
  }

  .storage-toggle-btn {
    justify-content: center;
  }

  .storage-header {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }

  .disk-health-grid {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .health-item {
    padding: 8px 10px;
  }

  .health-label {
    font-size: 10px;
  }

  .health-value {
    font-size: 13px;
  }
}
</style>