spring:
  application:
    name: 长安5G管理平台-后端管理服务
  profiles:
    # 环境切换配置
    # dev: 开发环境
    # prod: 生产环境
    active: ${SPRING_PROFILES_ACTIVE:prod}
  mvc:
    pathmatch:
      matching-strategy: ANT_PATH_MATCHER
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    default-property-inclusion: non_null
  datasource:
    hikari:
      max-lifetime: ${SPRING_DATASOURCE_HIKARI_MAX_LIFETIME:60000}

casbin:
  # If you are using a model profile at this address, no configuration is required
  tableName: casbin_rule
  model: classpath:casbin/rbac_model.conf
  enableWatcher: false

server:
  port: 8080
  # 优化大文件下载配置
  tomcat:
    connection-timeout: 300000 # 5分钟连接超时
    max-http-form-post-size: 100MB # 最大POST大小
    max-swallow-size: 100MB # 最大吞吐量
  servlet:
    multipart:
      max-file-size: 100MB # 最大文件大小
      max-request-size: 100MB # 最大请求大小

# 日志配置
logging:
  level:
    root: INFO
    com.unnet.jmanul: DEBUG
    org.springframework.web: INFO

mybatis-plus:
  # config-location: "classpath:mybatis/mybatis.config"
  mapper-locations:
    - "classpath*:mapper/**/*.xml"
    - "classpath*:mappers/**/*.xml"
    - "classpath*:com/unnet/jmanul/**/mapper/xml/*.xml"
  global-config:
    db-config:
      logic-delete-field: deleted_at  # 全局逻辑删除的实体字段名
      logic-delete-value: 1 # 逻辑已删除值(默认为 1)
      logic-not-delete-value: 0 # 逻辑未删除值(默认为 0)
